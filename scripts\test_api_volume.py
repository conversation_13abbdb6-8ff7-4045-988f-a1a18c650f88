#!/usr/bin/env python3
"""
Test the OHLCV API to see if it returns volume data
"""
import requests
import json

def test_api_volume():
    """Test if the API returns volume data"""
    print("🧪 Testing OHLCV API for volume data...")
    
    try:
        # Test the API endpoint
        url = "http://localhost:8000/api/v1/ohlcv/data"
        params = {
            "symbol": "BTCUSDT",
            "timeframe": "15m",
            "limit": 3
        }
        
        print(f"📡 Making request to: {url}")
        print(f"📋 Parameters: {params}")
        
        response = requests.get(url, params=params)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Response successful")
            print(f"📊 Response keys: {list(data.keys())}")
            
            if 'data' in data and 'ohlcv' in data['data']:
                ohlcv_data = data['data']['ohlcv']
                print(f"📈 OHLCV data count: {len(ohlcv_data)}")
                
                if ohlcv_data:
                    print(f"📊 Sample OHLCV records:")
                    for i, record in enumerate(ohlcv_data[:3]):
                        print(f"  Record {i+1}:")
                        print(f"    Timestamp: {record.get('timestamp')}")
                        print(f"    Open: {record.get('open')}")
                        print(f"    High: {record.get('high')}")
                        print(f"    Low: {record.get('low')}")
                        print(f"    Close: {record.get('close')}")
                        print(f"    Volume: {record.get('volume')} ({'✅ HAS VOLUME' if record.get('volume') and float(record.get('volume', 0)) > 0 else '❌ NO VOLUME'})")
                        print()
                else:
                    print("❌ No OHLCV data in response")
            else:
                print("❌ No OHLCV data structure in response")
                print(f"📋 Full response: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"📋 Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing API: {e}")

if __name__ == "__main__":
    test_api_volume()
