// Enhanced Multi-Panel Chart Manager
// Supports synchronized charts with main price, volume, and technical indicators

class MultiPanelChartManager {
    constructor() {
        this.charts = {};
        this.series = {};
        this.currentData = [];
        this.currentSymbol = 'BTCUSDT';
        this.currentTimeframe = '1m';
        this.crosshairSyncEnabled = true;
        this.isInitialized = false;
        
        // Chart configurations
        this.chartConfigs = {
            main: {
                container: 'mainChart',
                height: 450,
                priceScale: true,
                timeScale: true
            },
            volume: {
                container: 'volumeChart',
                height: 120,
                priceScale: false,
                timeScale: false
            },
            rsi: {
                container: 'rsiChart',
                height: 150,
                priceScale: true,
                timeScale: false
            },
            macd: {
                container: 'macdChart',
                height: 150,
                priceScale: true,
                timeScale: false
            }
        };
        
        this.waitForLibraryAndInit();
    }
    
    waitForLibraryAndInit() {
        if (typeof LightweightCharts !== 'undefined') {
            console.log('TradingView library ready, initializing multi-panel charts...');
            this.initCharts();
            this.initEventListeners();
            this.isInitialized = true;
        } else {
            console.log('Waiting for TradingView library to load...');
            setTimeout(() => this.waitForLibraryAndInit(), 100);
        }
    }
    
    initCharts() {
        // Common chart options
        const commonOptions = {
            layout: {
                backgroundColor: '#131722',
                textColor: '#d1d4dc',
                fontSize: 12,
                fontFamily: 'Trebuchet MS, sans-serif',
            },
            grid: {
                vertLines: {
                    color: '#363c4e',
                    style: 1,
                    visible: true,
                },
                horzLines: {
                    color: '#363c4e',
                    style: 1,
                    visible: true,
                },
            },
            crosshair: {
                mode: LightweightCharts.CrosshairMode.Normal,
                vertLine: {
                    color: '#758696',
                    width: 1,
                    style: 3,
                    visible: true,
                    labelVisible: true,
                },
                horzLine: {
                    color: '#758696',
                    width: 1,
                    style: 3,
                    visible: true,
                    labelVisible: true,
                },
            },
            rightPriceScale: {
                borderColor: '#485c7b',
                textColor: '#b2b5be',
                entireTextOnly: false,
            },
            timeScale: {
                borderColor: '#485c7b',
                textColor: '#b2b5be',
                timeVisible: true,
                secondsVisible: false,
            },
        };
        
        // Initialize each chart panel
        Object.keys(this.chartConfigs).forEach(panelId => {
            const config = this.chartConfigs[panelId];
            const container = document.getElementById(config.container);
            
            if (!container) {
                console.error(`Container ${config.container} not found for panel ${panelId}`);
                return;
            }
            
            // Panel-specific options
            const panelOptions = {
                ...commonOptions,
                width: container.clientWidth,
                height: config.height,
                rightPriceScale: {
                    ...commonOptions.rightPriceScale,
                    visible: config.priceScale,
                },
                timeScale: {
                    ...commonOptions.timeScale,
                    visible: config.timeScale,
                },
            };
            
            try {
                this.charts[panelId] = LightweightCharts.createChart(container, panelOptions);
                console.log(`Chart ${panelId} initialized successfully`);
                
                // Setup crosshair synchronization
                if (this.crosshairSyncEnabled) {
                    this.setupCrosshairSync(panelId);
                }
                
            } catch (error) {
                console.error(`Error creating chart ${panelId}:`, error);
            }
        });
        
        // Initialize series for each chart
        this.initSeries();
    }
    
    initSeries() {
        // Main chart - Candlestick series
        if (this.charts.main) {
            this.series.candlestick = this.charts.main.addCandlestickSeries({
                upColor: '#26a69a',
                downColor: '#ef5350',
                borderVisible: false,
                wickUpColor: '#26a69a',
                wickDownColor: '#ef5350',
            });
        }
        
        // Volume chart - Histogram series
        if (this.charts.volume) {
            this.series.volume = this.charts.volume.addHistogramSeries({
                color: '#26a69a',
                priceFormat: {
                    type: 'volume',
                },
                priceScaleId: '',
            });
        }
        
        // RSI chart - Line series
        if (this.charts.rsi) {
            this.series.rsi = this.charts.rsi.addLineSeries({
                color: '#2962ff',
                lineWidth: 2,
                priceFormat: {
                    type: 'price',
                    precision: 2,
                    minMove: 0.01,
                },
            });
            
            // Add RSI reference lines (30, 50, 70)
            this.addRSIReferenceLines();
        }
        
        // MACD chart - Multiple line series
        if (this.charts.macd) {
            this.series.macd = this.charts.macd.addLineSeries({
                color: '#2962ff',
                lineWidth: 2,
                title: 'MACD',
            });
            
            this.series.macdSignal = this.charts.macd.addLineSeries({
                color: '#ff6d00',
                lineWidth: 2,
                title: 'Signal',
            });
            
            this.series.macdHistogram = this.charts.macd.addHistogramSeries({
                color: '#26a69a',
                title: 'Histogram',
            });
        }
    }
    
    addRSIReferenceLines() {
        if (!this.charts.rsi) return;
        
        // Add horizontal reference lines for RSI
        const rsiLevels = [30, 50, 70];
        rsiLevels.forEach(level => {
            this.charts.rsi.addLineSeries({
                color: level === 50 ? '#666' : '#444',
                lineWidth: 1,
                lineStyle: 2, // Dashed
                lastValueVisible: false,
                priceLineVisible: false,
            }).setData([
                { time: '2020-01-01', value: level },
                { time: '2030-01-01', value: level }
            ]);
        });
    }
    
    setupCrosshairSync(panelId) {
        const chart = this.charts[panelId];
        if (!chart) return;

        chart.subscribeCrosshairMove((param) => {
            if (!param.time) {
                // Clear crosshair info when not hovering
                this.updateCrosshairInfo(null);
                return;
            }

            // Sync time scale across all charts
            Object.keys(this.charts).forEach(otherId => {
                if (otherId !== panelId && this.charts[otherId]) {
                    try {
                        // Sync the visible time range
                        const timeScale = this.charts[panelId].timeScale();
                        const visibleRange = timeScale.getVisibleRange();
                        if (visibleRange) {
                            this.charts[otherId].timeScale().setVisibleRange(visibleRange);
                        }
                    } catch (error) {
                        // Ignore sync errors
                    }
                }
            });

            // Update crosshair info with detailed data
            this.updateCrosshairInfo(param, panelId);
        });

        // Sync zoom and pan
        chart.timeScale().subscribeVisibleTimeRangeChange((timeRange) => {
            if (!timeRange) return;

            Object.keys(this.charts).forEach(otherId => {
                if (otherId !== panelId && this.charts[otherId]) {
                    try {
                        this.charts[otherId].timeScale().setVisibleRange(timeRange);
                    } catch (error) {
                        // Ignore sync errors
                    }
                }
            });
        });
    }
    
    updateCrosshairInfo(param, panelId) {
        const crosshairInfo = document.getElementById('crosshair-info');
        if (!crosshairInfo) return;

        if (!param || !param.time) {
            crosshairInfo.textContent = '';
            return;
        }

        const time = new Date(param.time * 1000);
        let info = `${time.toLocaleDateString()} ${time.toLocaleTimeString()}`;

        // Get data for the current time from all series
        if (param.seriesPrices && this.currentData.length > 0) {
            // Find the corresponding data point
            const dataPoint = this.currentData.find(d => d.timestamp === param.time);

            if (dataPoint) {
                info += ` | O: ${parseFloat(dataPoint.open).toFixed(2)}`;
                info += ` H: ${parseFloat(dataPoint.high).toFixed(2)}`;
                info += ` L: ${parseFloat(dataPoint.low).toFixed(2)}`;
                info += ` C: ${parseFloat(dataPoint.close).toFixed(2)}`;
                info += ` V: ${parseFloat(dataPoint.volume).toLocaleString()}`;
            }

            // Add series-specific prices
            Object.entries(param.seriesPrices).forEach(([seriesId, price]) => {
                if (price !== undefined && typeof price === 'object') {
                    // Candlestick data
                    if (price.close !== undefined) {
                        info += ` | Close: ${price.close.toFixed(2)}`;
                    }
                } else if (price !== undefined) {
                    // Line or histogram data
                    if (panelId === 'rsi') {
                        info += ` | RSI: ${price.toFixed(2)}`;
                    } else if (panelId === 'macd') {
                        info += ` | MACD: ${price.toFixed(4)}`;
                    }
                }
            });
        }

        crosshairInfo.textContent = info;
    }
    
    initEventListeners() {
        // Timeframe selector
        document.querySelectorAll('.timeframe-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.timeframe-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.currentTimeframe = e.target.dataset.timeframe;
                this.updateSymbolDisplay();
            });
        });
        
        // Symbol search
        const symbolInput = document.getElementById('symbolInput');
        const symbolSearch = document.getElementById('symbolSearch');
        
        if (symbolSearch) {
            symbolSearch.addEventListener('click', () => {
                const symbol = symbolInput.value.trim().toUpperCase();
                if (symbol) {
                    this.currentSymbol = symbol;
                    this.updateSymbolDisplay();
                }
            });
        }
        
        if (symbolInput) {
            symbolInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    symbolSearch.click();
                }
            });
        }
        
        // Panel toggles
        document.querySelectorAll('.panel-toggle').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const panel = e.target.dataset.panel;
                const chartPanel = e.target.closest('.chart-panel');
                chartPanel.classList.toggle('collapsed');
            });
        });
        
        // Reset zoom
        const resetZoom = document.getElementById('resetZoom');
        if (resetZoom) {
            resetZoom.addEventListener('click', () => {
                Object.values(this.charts).forEach(chart => {
                    if (chart && chart.timeScale) {
                        chart.timeScale().fitContent();
                    }
                });
            });
        }
        
        // Window resize handler
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }
    
    updateSymbolDisplay() {
        const symbolElement = document.getElementById('currentSymbol');
        const timeframeElement = document.getElementById('currentTimeframe');
        
        if (symbolElement) symbolElement.textContent = this.currentSymbol;
        if (timeframeElement) timeframeElement.textContent = this.currentTimeframe;
    }
    
    handleResize() {
        Object.keys(this.chartConfigs).forEach(panelId => {
            const config = this.chartConfigs[panelId];
            const container = document.getElementById(config.container);
            const chart = this.charts[panelId];
            
            if (container && chart) {
                chart.applyOptions({
                    width: container.clientWidth,
                    height: config.height,
                });
            }
        });
    }
    
    // Data loading methods
    loadData(ohlcvData, indicators = {}) {
        if (!this.isInitialized || !ohlcvData || ohlcvData.length === 0) {
            console.warn('Charts not initialized or no data provided');
            return;
        }

        const startTime = performance.now();
        this.currentData = ohlcvData;

        // Load main candlestick data
        if (this.series.candlestick) {
            const candleData = ohlcvData.map(item => ({
                time: item.timestamp,
                open: parseFloat(item.open),
                high: parseFloat(item.high),
                low: parseFloat(item.low),
                close: parseFloat(item.close),
            })).filter(item => !isNaN(item.open) && !isNaN(item.high) && !isNaN(item.low) && !isNaN(item.close));

            this.series.candlestick.setData(candleData);
        }

        // Load volume data
        if (this.series.volume) {
            const volumeData = ohlcvData.map(item => ({
                time: item.timestamp,
                value: parseFloat(item.volume),
                color: parseFloat(item.close) >= parseFloat(item.open) ? '#26a69a' : '#ef5350',
            })).filter(item => !isNaN(item.value));

            this.series.volume.setData(volumeData);
        }

        // Load indicator data
        this.loadIndicators(indicators);

        // Update price info
        this.updatePriceInfo();

        // Position charts to show earliest data instead of fitting all content
        setTimeout(() => {
            this.positionToEarliestData(ohlcvData);

            // Update performance info
            const loadTime = performance.now() - startTime;
            this.updatePerformanceInfo(loadTime, ohlcvData.length);


        }, 100);
    }

    positionToEarliestData(ohlcvData) {
        if (!ohlcvData || ohlcvData.length === 0) return;

        try {
            // Get the earliest timestamp from the data
            const earliestTime = ohlcvData[0].timestamp;
            const latestTime = ohlcvData[ohlcvData.length - 1].timestamp;

            // Calculate a reasonable visible range starting from the earliest data
            const visibleCandles = Math.min(200, Math.max(50, ohlcvData.length * 0.3));
            const timePerCandle = (latestTime - earliestTime) / (ohlcvData.length - 1);
            const visibleTimeRange = visibleCandles * timePerCandle;

            // Set visible range for all charts to start from the earliest data
            Object.values(this.charts).forEach(chart => {
                if (chart && chart.timeScale) {
                    chart.timeScale().setVisibleRange({
                        from: earliestTime,
                        to: earliestTime + visibleTimeRange
                    });
                }
            });

            console.log(`Multi-panel charts positioned to show earliest data from ${new Date(earliestTime * 1000).toISOString()}`);
        } catch (error) {
            console.error('Error positioning multi-panel charts to earliest data:', error);
            // Fallback to fitContent if positioning fails
            Object.values(this.charts).forEach(chart => {
                if (chart && chart.timeScale) {
                    chart.timeScale().fitContent();
                }
            });
        }
    }

    updatePerformanceInfo(loadTime, dataPoints) {
        const performanceInfo = document.getElementById('performance-info');
        if (performanceInfo) {
            performanceInfo.textContent = `Loaded ${dataPoints} points in ${loadTime.toFixed(1)}ms`;
        }
    }
    
    loadIndicators(indicators) {
        // Load RSI data
        if (indicators.rsi && this.series.rsi) {
            const rsiData = indicators.rsi.map((value, index) => ({
                time: this.currentData[index]?.timestamp,
                value: value,
            })).filter(item => item.time && !isNaN(item.value));
            
            this.series.rsi.setData(rsiData);
        }
        
        // Load MACD data
        if (indicators.macd && this.series.macd) {
            const macdData = indicators.macd.macd.map((value, index) => ({
                time: this.currentData[index]?.timestamp,
                value: value,
            })).filter(item => item.time && !isNaN(item.value));
            
            const signalData = indicators.macd.signal.map((value, index) => ({
                time: this.currentData[index]?.timestamp,
                value: value,
            })).filter(item => item.time && !isNaN(item.value));
            
            const histogramData = indicators.macd.histogram.map((value, index) => ({
                time: this.currentData[index]?.timestamp,
                value: value,
                color: value >= 0 ? '#26a69a' : '#ef5350',
            })).filter(item => item.time && !isNaN(item.value));
            
            this.series.macd.setData(macdData);
            this.series.macdSignal.setData(signalData);
            this.series.macdHistogram.setData(histogramData);
        }
    }
    
    updatePriceInfo() {
        if (!this.currentData || this.currentData.length === 0) return;
        
        const lastCandle = this.currentData[this.currentData.length - 1];
        const prevCandle = this.currentData[this.currentData.length - 2];
        
        if (!lastCandle) return;
        
        const price = parseFloat(lastCandle.close);
        const prevPrice = prevCandle ? parseFloat(prevCandle.close) : price;
        const change = price - prevPrice;
        const changePercent = prevPrice !== 0 ? (change / prevPrice) * 100 : 0;
        
        const priceElement = document.querySelector('.price-info .price');
        const changeElement = document.querySelector('.price-info .change');
        
        if (priceElement) {
            priceElement.textContent = `$${price.toFixed(2)}`;
        }
        
        if (changeElement) {
            const sign = change >= 0 ? '+' : '';
            changeElement.textContent = `${sign}${changePercent.toFixed(2)}%`;
            changeElement.className = `change ${change >= 0 ? 'positive' : 'negative'}`;
        }
    }
    
    // Public API methods
    updateData(newData) {
        if (!this.isInitialized) return;
        
        // Update the last candle or add new candle
        if (this.series.candlestick && newData) {
            this.series.candlestick.update({
                time: newData.timestamp,
                open: parseFloat(newData.open),
                high: parseFloat(newData.high),
                low: parseFloat(newData.low),
                close: parseFloat(newData.close),
            });
        }
        
        if (this.series.volume && newData) {
            this.series.volume.update({
                time: newData.timestamp,
                value: parseFloat(newData.volume),
                color: parseFloat(newData.close) >= parseFloat(newData.open) ? '#26a69a' : '#ef5350',
            });
        }
    }
    
    // Advanced features
    addTradingMarks(marks) {
        if (!this.series.candlestick || !marks || marks.length === 0) return;

        const markers = marks.map(mark => ({
            time: mark.timestamp,
            position: mark.type === 'buy' ? 'belowBar' : 'aboveBar',
            color: mark.type === 'buy' ? '#26a69a' : '#ef5350',
            shape: mark.type === 'buy' ? 'arrowUp' : 'arrowDown',
            text: `${mark.type.toUpperCase()} @ ${mark.price}`,
        }));

        this.series.candlestick.setMarkers(markers);
    }

    enableRealtimeUpdates(interval = 1000) {
        if (this.realtimeInterval) {
            clearInterval(this.realtimeInterval);
        }

        this.realtimeInterval = setInterval(() => {
            // Simulate real-time price updates
            if (this.currentData && this.currentData.length > 0) {
                const lastCandle = this.currentData[this.currentData.length - 1];
                const newPrice = parseFloat(lastCandle.close) + (Math.random() - 0.5) * 10;

                const updatedCandle = {
                    ...lastCandle,
                    close: newPrice.toFixed(2),
                    high: Math.max(parseFloat(lastCandle.high), newPrice).toFixed(2),
                    low: Math.min(parseFloat(lastCandle.low), newPrice).toFixed(2),
                };

                this.updateData(updatedCandle);
            }
        }, interval);
    }

    disableRealtimeUpdates() {
        if (this.realtimeInterval) {
            clearInterval(this.realtimeInterval);
            this.realtimeInterval = null;
        }
    }

    exportChartImage(panelId = 'main') {
        const chart = this.charts[panelId];
        if (!chart) return null;

        try {
            // This would require additional implementation for image export
            console.log(`Export functionality for ${panelId} panel would be implemented here`);
            return null;
        } catch (error) {
            console.error('Error exporting chart image:', error);
            return null;
        }
    }

    destroy() {
        // Clean up real-time updates
        this.disableRealtimeUpdates();

        // Remove all charts
        Object.values(this.charts).forEach(chart => {
            if (chart) {
                chart.remove();
            }
        });

        // Clear references
        this.charts = {};
        this.series = {};
        this.currentData = [];
        this.isInitialized = false;
    }
}

// Global instance
let multiPanelChartManager = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    multiPanelChartManager = new MultiPanelChartManager();
    window.multiPanelChartManager = multiPanelChartManager;
    console.log('Multi-panel chart manager initialized');
});
