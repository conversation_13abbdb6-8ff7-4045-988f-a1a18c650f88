"""
Data access layer using PyMySQL
"""
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import json

from app.core.database import get_db_cursor, DatabaseError

logger = logging.getLogger(__name__)


class OHLCVDataAccess:
    """OHLCV data access methods"""
    
    @staticmethod
    def insert_ohlcv(symbol: str, timeframe: str, timestamp: datetime,
                     open_price: float, high: float, low: float, close: float, volume: float,
                     strategy_id: Optional[int] = None, exchange: str = 'binance') -> int:
        """Insert OHLCV data"""
        try:
            with get_db_cursor() as cursor:
                sql = """
                INSERT IGNORE INTO ohlcv_data
                (strategy_id, symbol, timeframe, timestamp, `open`, high, low, `close`, volume, exchange)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (strategy_id, symbol, timeframe, timestamp, open_price, high, low, close, volume, exchange))
                return cursor.rowcount
        except Exception as e:
            logger.error(f"Failed to insert OHLCV data: {e}")
            raise DatabaseError(f"OHLCV insert failed: {e}")

    @staticmethod
    def check_existing_data_range(symbol: str, timeframe: str, start_time: datetime,
                                  end_time: datetime, strategy_id: Optional[int] = None) -> Dict[str, Any]:
        """Check what data already exists in the specified range"""
        try:
            with get_db_cursor() as cursor:
                sql = """
                SELECT
                    MIN(timestamp) as earliest_timestamp,
                    MAX(timestamp) as latest_timestamp,
                    COUNT(*) as total_count,
                    COUNT(DISTINCT DATE(timestamp)) as unique_days
                FROM ohlcv_data
                WHERE symbol = %s AND timeframe = %s
                AND timestamp >= %s AND timestamp <= %s
                """
                params = [symbol, timeframe, start_time, end_time]

                if strategy_id is not None:
                    sql += " AND strategy_id = %s"
                    params.append(strategy_id)

                cursor.execute(sql, params)
                result = cursor.fetchone()

                return {
                    'exists': result['total_count'] > 0,
                    'count': result['total_count'] or 0,
                    'earliest': result['earliest_timestamp'],
                    'latest': result['latest_timestamp'],
                    'unique_days': result['unique_days'] or 0
                }
        except Exception as e:
            logger.error(f"Failed to check existing data range: {e}")
            raise DatabaseError(f"Data range check failed: {e}")

    @staticmethod
    def get_missing_data_ranges(symbol: str, timeframe: str, start_time: datetime,
                               end_time: datetime, strategy_id: Optional[int] = None) -> List[Dict[str, datetime]]:
        """Identify missing data ranges that need to be fetched"""
        try:
            with get_db_cursor() as cursor:
                # Get all existing timestamps in the range, ordered
                sql = """
                SELECT timestamp
                FROM ohlcv_data
                WHERE symbol = %s AND timeframe = %s
                AND timestamp >= %s AND timestamp <= %s
                """
                params = [symbol, timeframe, start_time, end_time]

                if strategy_id is not None:
                    sql += " AND strategy_id = %s"
                    params.append(strategy_id)

                sql += " ORDER BY timestamp ASC"
                cursor.execute(sql, params)
                existing_timestamps = [row['timestamp'] for row in cursor.fetchall()]

                if not existing_timestamps:
                    # No data exists, return the full range
                    return [{'start': start_time, 'end': end_time}]

                missing_ranges = []

                # Check for gap before first existing timestamp
                if existing_timestamps[0] > start_time:
                    missing_ranges.append({
                        'start': start_time,
                        'end': existing_timestamps[0]
                    })

                # Check for gaps between existing timestamps
                # This is simplified - for production, you'd want more sophisticated gap detection
                # based on the expected interval between candles

                # Check for gap after last existing timestamp
                if existing_timestamps[-1] < end_time:
                    missing_ranges.append({
                        'start': existing_timestamps[-1],
                        'end': end_time
                    })

                return missing_ranges

        except Exception as e:
            logger.error(f"Failed to get missing data ranges: {e}")
            raise DatabaseError(f"Missing data ranges check failed: {e}")

    @staticmethod
    def get_ohlcv_data(symbol: str, timeframe: str, limit: Optional[int] = None,
                       start_time: Optional[datetime] = None,
                       end_time: Optional[datetime] = None,
                       strategy_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get OHLCV data with optional strategy filtering"""
        try:
            with get_db_cursor() as cursor:
                sql = """
                SELECT symbol, timeframe, timestamp, `open`, high, low, `close`, volume, created_at
                FROM ohlcv_data
                WHERE symbol = %s AND timeframe = %s
                """
                params = [symbol, timeframe]

                if strategy_id is not None:
                    sql += " AND strategy_id = %s"
                    params.append(strategy_id)

                if start_time:
                    sql += " AND timestamp >= %s"
                    params.append(start_time)

                if end_time:
                    sql += " AND timestamp <= %s"
                    params.append(end_time)

                sql += " ORDER BY timestamp ASC"

                # Add limit only if specified
                if limit is not None:
                    sql += " LIMIT %s"
                    params.append(limit)

                cursor.execute(sql, params)
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"Failed to get OHLCV data: {e}")
            raise DatabaseError(f"OHLCV query failed: {e}")
    
    @staticmethod
    def get_symbols() -> List[str]:
        """Get available symbols"""
        try:
            with get_db_cursor() as cursor:
                cursor.execute("SELECT DISTINCT symbol FROM ohlcv_data ORDER BY symbol")
                return [row['symbol'] for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Failed to get symbols: {e}")
            raise DatabaseError(f"Symbols query failed: {e}")

    @staticmethod
    def get_earliest_date(symbol: str, timeframe: str, strategy_id: Optional[int] = None) -> Optional[datetime]:
        """Get the earliest available date for a symbol/timeframe"""
        try:
            with get_db_cursor() as cursor:
                if strategy_id is not None:
                    # Filter by specific strategy
                    sql = """
                    SELECT MIN(timestamp) as earliest_date
                    FROM ohlcv_data
                    WHERE symbol = %s AND timeframe = %s AND strategy_id = %s
                    """
                    params = [symbol, timeframe, strategy_id]
                else:
                    # Get earliest date across all strategies for this symbol/timeframe
                    sql = """
                    SELECT MIN(timestamp) as earliest_date
                    FROM ohlcv_data
                    WHERE symbol = %s AND timeframe = %s
                    """
                    params = [symbol, timeframe]

                logger.info(f"Executing earliest date query: {sql} with params: {params}")
                cursor.execute(sql, params)
                result = cursor.fetchone()
                earliest_date = result['earliest_date'] if result and result['earliest_date'] else None
                logger.info(f"Earliest date result: {earliest_date}")
                return earliest_date
        except Exception as e:
            logger.error(f"Failed to get earliest date: {e}")
            raise DatabaseError(f"Earliest date query failed: {e}")

    @staticmethod
    def get_monthly_data(symbol: str, timeframe: str, year: int, month: int,
                        strategy_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get OHLCV data for a specific month"""
        try:
            with get_db_cursor() as cursor:
                # Calculate start and end of month
                start_date = datetime(year, month, 1)
                if month == 12:
                    end_date = datetime(year + 1, 1, 1)
                else:
                    end_date = datetime(year, month + 1, 1)

                sql = """
                SELECT symbol, timeframe, timestamp, `open`, high, low, `close`, volume, created_at
                FROM ohlcv_data
                WHERE symbol = %s AND timeframe = %s
                AND timestamp >= %s AND timestamp < %s
                """
                params = [symbol, timeframe, start_date, end_date]

                if strategy_id is not None:
                    sql += " AND strategy_id = %s"
                    params.append(strategy_id)

                sql += " ORDER BY timestamp ASC"

                logger.info(f"Executing monthly data query: {sql} with params: {params}")
                cursor.execute(sql, params)
                results = cursor.fetchall()
                logger.info(f"Monthly data query returned {len(results)} records")
                return results
        except Exception as e:
            logger.error(f"Failed to get monthly data: {e}")
            raise DatabaseError(f"Monthly data query failed: {e}")


class IndicatorsDataAccess:
    """Indicators data access methods"""
    
    @staticmethod
    def insert_indicators(symbol: str, timeframe: str, timestamp: datetime,
                         indicator_name: str, config: Dict[str, Any], values: Dict[str, Any]) -> int:
        """Insert indicator data"""
        try:
            with get_db_cursor() as cursor:
                sql = """
                INSERT INTO indicators_data 
                (symbol, timeframe, timestamp, indicator_name, indicator_config, indicator_values)
                VALUES (%s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE 
                indicator_config = VALUES(indicator_config),
                indicator_values = VALUES(indicator_values)
                """
                cursor.execute(sql, (symbol, timeframe, timestamp, indicator_name, 
                                   json.dumps(config), json.dumps(values)))
                return cursor.rowcount
        except Exception as e:
            logger.error(f"Failed to insert indicators: {e}")
            raise DatabaseError(f"Indicators insert failed: {e}")
    
    @staticmethod
    def get_indicators(symbol: str, timeframe: str, indicator_name: str, 
                      limit: int = 500) -> List[Dict[str, Any]]:
        """Get indicator data"""
        try:
            with get_db_cursor() as cursor:
                sql = """
                SELECT timestamp, indicator_config, indicator_values
                FROM indicators_data 
                WHERE symbol = %s AND timeframe = %s AND indicator_name = %s
                ORDER BY timestamp DESC LIMIT %s
                """
                cursor.execute(sql, (symbol, timeframe, indicator_name, limit))
                results = cursor.fetchall()
                
                # Parse JSON fields
                for result in results:
                    result['indicator_config'] = json.loads(result['indicator_config'])
                    result['indicator_values'] = json.loads(result['indicator_values'])
                
                return results
        except Exception as e:
            logger.error(f"Failed to get indicators: {e}")
            raise DatabaseError(f"Indicators query failed: {e}")


class TradeDataAccess:
    """Trade data access methods"""
    
    @staticmethod
    def insert_mark(symbol: str, timeframe: str, mark_type: str, entry_side: Optional[str],
                   timestamp: datetime, price: float, indicator_snapshot: Optional[Dict] = None,
                   ohlcv_snapshot: Optional[Dict] = None, linked_trade_id: Optional[int] = None) -> int:
        """Insert trade mark"""
        try:
            with get_db_cursor() as cursor:
                sql = """
                INSERT INTO manual_marks 
                (symbol, timeframe, mark_type, entry_side, timestamp, price, 
                 indicator_snapshot, ohlcv_snapshot, linked_trade_id)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (
                    symbol, timeframe, mark_type, entry_side, timestamp, price,
                    json.dumps(indicator_snapshot) if indicator_snapshot else None,
                    json.dumps(ohlcv_snapshot) if ohlcv_snapshot else None,
                    linked_trade_id
                ))
                return cursor.lastrowid
        except Exception as e:
            logger.error(f"Failed to insert trade mark: {e}")
            raise DatabaseError(f"Trade mark insert failed: {e}")
    
    @staticmethod
    def get_marks(symbol: Optional[str] = None, timeframe: Optional[str] = None,
                 mark_type: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get trade marks"""
        try:
            with get_db_cursor() as cursor:
                sql = "SELECT * FROM manual_marks WHERE 1=1"
                params = []
                
                if symbol:
                    sql += " AND symbol = %s"
                    params.append(symbol)
                
                if timeframe:
                    sql += " AND timeframe = %s"
                    params.append(timeframe)
                
                if mark_type:
                    sql += " AND mark_type = %s"
                    params.append(mark_type)
                
                sql += " ORDER BY timestamp DESC LIMIT %s"
                params.append(limit)
                
                cursor.execute(sql, params)
                results = cursor.fetchall()
                
                # Parse JSON fields
                for result in results:
                    if result['indicator_snapshot']:
                        result['indicator_snapshot'] = json.loads(result['indicator_snapshot'])
                    if result['ohlcv_snapshot']:
                        result['ohlcv_snapshot'] = json.loads(result['ohlcv_snapshot'])
                
                return results
        except Exception as e:
            logger.error(f"Failed to get trade marks: {e}")
            raise DatabaseError(f"Trade marks query failed: {e}")
    
    @staticmethod
    def get_mark_by_id(mark_id: int) -> Optional[Dict[str, Any]]:
        """Get trade mark by ID"""
        try:
            with get_db_cursor() as cursor:
                cursor.execute("SELECT * FROM manual_marks WHERE id = %s", (mark_id,))
                result = cursor.fetchone()
                
                if result:
                    if result['indicator_snapshot']:
                        result['indicator_snapshot'] = json.loads(result['indicator_snapshot'])
                    if result['ohlcv_snapshot']:
                        result['ohlcv_snapshot'] = json.loads(result['ohlcv_snapshot'])
                
                return result
        except Exception as e:
            logger.error(f"Failed to get trade mark by ID: {e}")
            raise DatabaseError(f"Trade mark query failed: {e}")
    
    @staticmethod
    def insert_strategy_log(symbol: str, timeframe: str, entry_id: int, exit_id: int,
                           entry_side: str, profit_pct: float, entry_ohlcv: Optional[Dict] = None,
                           exit_ohlcv: Optional[Dict] = None, entry_indicators: Optional[Dict] = None,
                           exit_indicators: Optional[Dict] = None) -> int:
        """Insert strategy log entry"""
        try:
            with get_db_cursor() as cursor:
                sql = """
                INSERT INTO strategy_log 
                (symbol, timeframe, entry_id, exit_id, entry_side, profit_pct,
                 entry_ohlcv, exit_ohlcv, entry_indicator_snapshot, exit_indicator_snapshot)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (
                    symbol, timeframe, entry_id, exit_id, entry_side, profit_pct,
                    json.dumps(entry_ohlcv) if entry_ohlcv else None,
                    json.dumps(exit_ohlcv) if exit_ohlcv else None,
                    json.dumps(entry_indicators) if entry_indicators else None,
                    json.dumps(exit_indicators) if exit_indicators else None
                ))
                return cursor.lastrowid
        except Exception as e:
            logger.error(f"Failed to insert strategy log: {e}")
            raise DatabaseError(f"Strategy log insert failed: {e}")
    
    @staticmethod
    def get_strategy_log(symbol: Optional[str] = None, timeframe: Optional[str] = None,
                        limit: int = 100) -> List[Dict[str, Any]]:
        """Get strategy log"""
        try:
            with get_db_cursor() as cursor:
                sql = "SELECT * FROM strategy_log WHERE 1=1"
                params = []
                
                if symbol:
                    sql += " AND symbol = %s"
                    params.append(symbol)
                
                if timeframe:
                    sql += " AND timeframe = %s"
                    params.append(timeframe)
                
                sql += " ORDER BY created_at DESC LIMIT %s"
                params.append(limit)
                
                cursor.execute(sql, params)
                results = cursor.fetchall()
                
                # Parse JSON fields
                for result in results:
                    for field in ['entry_ohlcv', 'exit_ohlcv', 'entry_indicator_snapshot', 'exit_indicator_snapshot']:
                        if result[field]:
                            result[field] = json.loads(result[field])
                
                return results
        except Exception as e:
            logger.error(f"Failed to get strategy log: {e}")
            raise DatabaseError(f"Strategy log query failed: {e}")
