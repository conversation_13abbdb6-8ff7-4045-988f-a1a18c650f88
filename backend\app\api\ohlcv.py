"""
Enhanced OHLCV Data API Endpoints with proper validation and error handling
"""
from fastapi import APIRouter, HTTPException, Query, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging
import asyncio

from app.core.database import DatabaseError
from app.core.data_access import OHLCVDataAccess
from app.core.exceptions import ExchangeError, ValidationError, RateLimitError
from app.core.validators import OHLCVFetchRequest, SymbolValidator, TimeframeValidator, ExchangeValidator
from app.services.binance_client import BinanceClient
from app.services.mexc_client import MEXCClient
from app.services.batch_fetcher import BatchOHLCVFetcher

logger = logging.getLogger(__name__)
router = APIRouter()

# Response models for better API documentation
class OHLCVResponse(BaseModel):
    """OHLCV response model"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class HealthResponse(BaseModel):
    """Health check response model"""
    success: bool
    exchange: str
    message: str
    rate_limit_status: Optional[Dict[str, Any]] = None

@router.post("/fetch", response_model=OHLCVResponse)
async def fetch_ohlcv_data(request: OHLCVFetchRequest) -> OHLCVResponse:
    """
    Fetch OHLCV data from exchange using intelligent batch processing

    This endpoint validates existing data and fetches complete date range
    in optimal 1000-candle batches, preventing duplicates and ensuring
    complete coverage of the requested time period.
    """
    try:
        # Validate date range is provided for batch processing
        if not request.start_time or not request.end_time:
            raise ValidationError("Both start_time and end_time are required for intelligent batch fetching")

        if request.start_time >= request.end_time:
            raise ValidationError("start_time must be before end_time")

        logger.info(f"Starting intelligent batch fetch for {request.symbol} {request.timeframe} "
                   f"from {request.start_time} to {request.end_time}")

        # Use batch fetcher for complete range extraction
        batch_fetcher = BatchOHLCVFetcher()
        batch_result = await batch_fetcher.fetch_complete_range(
            symbol=request.symbol,
            timeframe=request.timeframe,
            exchange=request.exchange,
            start_time=request.start_time,
            end_time=request.end_time,
            strategy_id=request.strategy_id
        )

        if not batch_result['success']:
            return OHLCVResponse(
                success=False,
                message=f"Batch fetch failed: {batch_result.get('error', 'Unknown error')}",
                error=batch_result.get('error', 'Unknown error')
            )

        # Get the final data for response
        final_ohlcv_data = OHLCVDataAccess.get_ohlcv_data(
            symbol=request.symbol,
            timeframe=request.timeframe,
            start_time=request.start_time,
            end_time=request.end_time,
            strategy_id=request.strategy_id
        )

        # Create comprehensive response message
        data_summary = batch_result['data_summary']
        batch_summary = batch_result['batch_summary']

        message_parts = [
            f"Intelligent batch fetch completed: {data_summary['final_count']} total candles in database"
        ]

        if data_summary['total_fetched'] > 0:
            message_parts.append(f"Fetched {data_summary['total_fetched']} candles in {batch_summary['total_batches']} batches")
            message_parts.append(f"Inserted {data_summary['total_inserted']} new records")

            if data_summary['total_duplicates'] > 0:
                message_parts.append(f"Skipped {data_summary['total_duplicates']} duplicates")

        if batch_summary['skipped_batches'] > 0:
            message_parts.append(f"Skipped {batch_summary['skipped_batches']} batches (data already exists)")

        return OHLCVResponse(
            success=True,
            message=" | ".join(message_parts),
            data={
                "ohlcv": final_ohlcv_data,
                "batch_result": batch_result,
                "data_count": len(final_ohlcv_data),
                "symbol": request.symbol,
                "timeframe": request.timeframe,
                "exchange": request.exchange
            }
        )

    except ValidationError as e:
        logger.warning(f"Validation error: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

    except RateLimitError as e:
        logger.warning(f"Rate limit error: {e}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=f"Rate limit exceeded. Retry after {e.retry_after} seconds" if e.retry_after else "Rate limit exceeded"
        )

    except ExchangeError as e:
        logger.error(f"Exchange error: {e}")
        raise HTTPException(status_code=status.HTTP_502_BAD_GATEWAY, detail=f"Exchange error: {e.message}")

    except DatabaseError as e:
        logger.error(f"Database error: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Database operation failed")

    except Exception as e:
        logger.error(f"Unexpected error fetching OHLCV data: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/fetch", response_model=OHLCVResponse)
async def fetch_ohlcv_data_get(
    symbol: str = Query(..., description="Trading symbol (e.g., BTCUSDT)"),
    timeframe: str = Query(..., description="Timeframe (1m, 5m, 15m, 1h, 4h, 1d)"),
    exchange: str = Query(..., description="Exchange name (binance or mexc)"),
    limit: Optional[int] = Query(None, description="Number of candles to fetch (optional)", ge=1, le=10000),
    start_time: Optional[str] = Query(None, description="Start time (ISO format)"),
    end_time: Optional[str] = Query(None, description="End time (ISO format)")
) -> OHLCVResponse:
    """
    Fetch OHLCV data from exchange and store in database (GET version)

    This endpoint fetches candlestick data from the specified exchange
    and stores it in the database, avoiding duplicates.
    """
    # Convert query parameters to request object
    request = OHLCVFetchRequest(
        symbol=symbol,
        timeframe=timeframe,
        exchange=exchange,
        limit=limit,  # Now optional, can be None
        start_time=datetime.fromisoformat(start_time) if start_time else None,
        end_time=datetime.fromisoformat(end_time) if end_time else None
    )

    # Call the existing POST endpoint logic
    return await fetch_ohlcv_data(request)


@router.get("/data", response_model=OHLCVResponse)
async def get_ohlcv_data(
    symbol: str = Query(..., description="Trading pair symbol"),
    timeframe: str = Query(..., description="Timeframe"),
    limit: Optional[int] = Query(None, ge=1, le=10000, description="Number of records (optional, fetches all if not specified)"),
    start_time: Optional[str] = Query(None, description="Start time (ISO format)"),
    end_time: Optional[str] = Query(None, description="End time (ISO format)"),
    strategy_id: Optional[int] = Query(None, description="Strategy ID to filter by")
) -> OHLCVResponse:
    """Get OHLCV data from database"""
    try:
        # Validate inputs
        symbol_validator = SymbolValidator(symbol=symbol)
        timeframe_validator = TimeframeValidator(timeframe=timeframe)

        # Parse timestamps if provided
        start_dt = None
        end_dt = None
        if start_time:
            start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
        if end_time:
            end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))

        # Get data from database
        ohlcv_data = OHLCVDataAccess.get_ohlcv_data(
            symbol=symbol_validator.symbol,
            timeframe=timeframe_validator.timeframe,
            limit=limit,
            start_time=start_dt,
            end_time=end_dt,
            strategy_id=strategy_id
        )

        if not ohlcv_data:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No data found")

        return OHLCVResponse(
            success=True,
            message=f"Retrieved {len(ohlcv_data)} OHLCV records",
            data={
                "symbol": symbol_validator.symbol,
                "timeframe": timeframe_validator.timeframe,
                "count": len(ohlcv_data),
                "ohlcv": ohlcv_data
            }
        )

    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except DatabaseError as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Database error")
    except Exception as e:
        logger.error(f"Error getting OHLCV data: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/earliest-date")
async def get_earliest_date(
    symbol: str = Query(..., description="Trading pair symbol"),
    timeframe: str = Query(..., description="Timeframe"),
    strategy_id: Optional[int] = Query(None, description="Strategy ID to filter by")
):
    """Get the earliest available date for a symbol/timeframe"""
    try:
        # Validate inputs
        symbol_validator = SymbolValidator(symbol=symbol)
        timeframe_validator = TimeframeValidator(timeframe=timeframe)

        earliest_date = OHLCVDataAccess.get_earliest_date(
            symbol=symbol_validator.symbol,
            timeframe=timeframe_validator.timeframe,
            strategy_id=strategy_id
        )

        if not earliest_date:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No data found")

        return {
            "success": True,
            "data": {
                "earliest_date": earliest_date.isoformat(),
                "year": earliest_date.year,
                "month": earliest_date.month,
                "symbol": symbol_validator.symbol,
                "timeframe": timeframe_validator.timeframe
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting earliest date: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/monthly-data", response_model=OHLCVResponse)
async def get_monthly_data(
    symbol: str = Query(..., description="Trading pair symbol"),
    timeframe: str = Query(..., description="Timeframe"),
    year: int = Query(..., description="Year", ge=2000, le=2030),
    month: int = Query(..., description="Month", ge=1, le=12),
    strategy_id: Optional[int] = Query(None, description="Strategy ID to filter by")
) -> OHLCVResponse:
    """Get OHLCV data for a specific month"""
    try:
        # Validate inputs
        symbol_validator = SymbolValidator(symbol=symbol)
        timeframe_validator = TimeframeValidator(timeframe=timeframe)

        # Get monthly data
        ohlcv_data = OHLCVDataAccess.get_monthly_data(
            symbol=symbol_validator.symbol,
            timeframe=timeframe_validator.timeframe,
            year=year,
            month=month,
            strategy_id=strategy_id
        )

        if not ohlcv_data:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No data found for this month")

        return OHLCVResponse(
            success=True,
            message=f"Retrieved {len(ohlcv_data)} OHLCV records for {year}-{month:02d}",
            data={
                "symbol": symbol_validator.symbol,
                "timeframe": timeframe_validator.timeframe,
                "count": len(ohlcv_data),
                "ohlcv": ohlcv_data,
                "year": year,
                "month": month
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving monthly data: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/test-connection", response_model=HealthResponse)
async def test_exchange_connection(
    exchange: str = Query(..., description="Exchange name (binance or mexc)")
) -> HealthResponse:
    """Test exchange API connection"""
    try:
        exchange_validator = ExchangeValidator(exchange=exchange)

        if exchange_validator.exchange == "binance":
            client = BinanceClient()
        elif exchange_validator.exchange == "mexc":
            client = MEXCClient()
        else:
            raise ValidationError(f"Unsupported exchange: {exchange}")

        # Test connection
        connection_ok = await client.test_connection()

        # Get rate limit status if available
        rate_limit_status = None
        if hasattr(client, 'get_rate_limit_status'):
            rate_limit_status = client.get_rate_limit_status()

        return HealthResponse(
            success=connection_ok,
            exchange=exchange_validator.exchange,
            message="Connection successful" if connection_ok else "Connection failed",
            rate_limit_status=rate_limit_status
        )

    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Error testing exchange connection: {e}")
        return HealthResponse(
            success=False,
            exchange=exchange,
            message=f"Connection test failed: {e}"
        )


@router.get("/symbols")
async def get_symbols(
    exchange: Optional[str] = Query(None, description="Exchange name")
):
    """Get available symbols"""
    try:
        # Get symbols from database
        db_symbols = OHLCVDataAccess.get_symbols()

        result = {
            "success": True,
            "data": {
                "database_symbols": db_symbols,
                "exchange_symbols": []
            }
        }

        # Get symbols from exchange if specified
        if exchange:
            exchange_validator = ExchangeValidator(exchange=exchange)

            if exchange_validator.exchange == "binance":
                client = BinanceClient()
            elif exchange_validator.exchange == "mexc":
                client = MEXCClient()
            else:
                raise ValidationError(f"Unsupported exchange: {exchange}")

            try:
                # This would require implementing get_symbols method in clients
                # For now, just return empty list
                result["data"]["exchange_symbols"] = []
                result["data"]["exchange"] = exchange_validator.exchange
            except Exception as e:
                logger.warning(f"Failed to get symbols from {exchange}: {e}")

        return result

    except Exception as e:
        logger.error(f"Error getting symbols: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")
