#!/usr/bin/env python3
"""
Database Schema Validation Script
Validates the current structure of the strategy_builder database
"""

import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from app.core.database import get_db_cursor

def validate_ohlcv_schema():
    """Validate the ohlcv_data table schema"""
    try:
        with get_db_cursor() as cursor:
            print("🔍 Checking ohlcv_data table structure...")
            
            # Get table structure
            cursor.execute("DESCRIBE ohlcv_data")
            columns = cursor.fetchall()
            
            print("\n📊 Current ohlcv_data table structure:")
            print("-" * 80)
            print(f"{'Field':<20} {'Type':<25} {'Null':<8} {'Key':<8} {'Default':<15} {'Extra'}")
            print("-" * 80)
            
            for col in columns:
                print(f"{col['Field']:<20} {col['Type']:<25} {col['Null']:<8} {col['Key']:<8} {str(col['Default']):<15} {col['Extra']}")
            
            # Check for required columns
            column_names = [col['Field'] for col in columns]
            required_columns = ['id', 'strategy_id', 'symbol', 'timeframe', 'timestamp', 'open', 'high', 'low', 'close', 'volume', 'exchange']
            
            print(f"\n✅ Found columns: {column_names}")
            
            missing_columns = [col for col in required_columns if col not in column_names]
            if missing_columns:
                print(f"❌ Missing required columns: {missing_columns}")
                return False
            else:
                print("✅ All required columns present")
            
            # Check data count
            cursor.execute("SELECT COUNT(*) as total_count FROM ohlcv_data")
            total_count = cursor.fetchone()['total_count']
            print(f"\n📈 Total records in ohlcv_data: {total_count}")
            
            # Check data distribution by strategy_id
            cursor.execute("""
                SELECT 
                    strategy_id,
                    COUNT(*) as count,
                    MIN(timestamp) as earliest,
                    MAX(timestamp) as latest
                FROM ohlcv_data 
                GROUP BY strategy_id 
                ORDER BY strategy_id
            """)
            strategy_data = cursor.fetchall()
            
            print("\n📊 Data distribution by strategy_id:")
            print("-" * 80)
            print(f"{'Strategy ID':<12} {'Count':<10} {'Earliest':<20} {'Latest'}")
            print("-" * 80)
            
            for row in strategy_data:
                strategy_id = row['strategy_id'] if row['strategy_id'] is not None else 'NULL'
                earliest = row['earliest'].strftime('%Y-%m-%d %H:%M') if row['earliest'] else 'N/A'
                latest = row['latest'].strftime('%Y-%m-%d %H:%M') if row['latest'] else 'N/A'
                print(f"{str(strategy_id):<12} {row['count']:<10} {earliest:<20} {latest}")
            
            # Check symbols and timeframes
            cursor.execute("SELECT DISTINCT symbol, timeframe FROM ohlcv_data ORDER BY symbol, timeframe")
            symbols_timeframes = cursor.fetchall()
            
            print(f"\n🎯 Available symbol/timeframe combinations:")
            for row in symbols_timeframes:
                print(f"  - {row['symbol']} / {row['timeframe']}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error validating schema: {e}")
        return False

def validate_strategies_table():
    """Validate the strategies table"""
    try:
        with get_db_cursor() as cursor:
            print("\n🔍 Checking strategies table...")
            
            cursor.execute("SELECT id, name, symbol, timeframe, exchange FROM strategies ORDER BY id")
            strategies = cursor.fetchall()
            
            print(f"\n📋 Available strategies ({len(strategies)} total):")
            print("-" * 80)
            print(f"{'ID':<5} {'Name':<25} {'Symbol':<10} {'Timeframe':<10} {'Exchange'}")
            print("-" * 80)
            
            for strategy in strategies:
                print(f"{strategy['id']:<5} {strategy['name']:<25} {strategy['symbol']:<10} {strategy['timeframe']:<10} {strategy['exchange']}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error validating strategies table: {e}")
        return False

def test_earliest_date_query():
    """Test the earliest date query"""
    try:
        with get_db_cursor() as cursor:
            print("\n🧪 Testing earliest date queries...")
            
            # Test without strategy_id filter
            cursor.execute("""
                SELECT 
                    symbol,
                    timeframe,
                    MIN(timestamp) as earliest_date,
                    COUNT(*) as total_records
                FROM ohlcv_data 
                GROUP BY symbol, timeframe
                ORDER BY symbol, timeframe
            """)
            results = cursor.fetchall()
            
            print("\n📅 Earliest dates by symbol/timeframe (all strategies):")
            print("-" * 80)
            print(f"{'Symbol':<10} {'Timeframe':<10} {'Earliest Date':<20} {'Records'}")
            print("-" * 80)
            
            for row in results:
                earliest = row['earliest_date'].strftime('%Y-%m-%d %H:%M') if row['earliest_date'] else 'N/A'
                print(f"{row['symbol']:<10} {row['timeframe']:<10} {earliest:<20} {row['total_records']}")
            
            # Test with specific strategy_id
            cursor.execute("""
                SELECT 
                    strategy_id,
                    symbol,
                    timeframe,
                    MIN(timestamp) as earliest_date,
                    COUNT(*) as total_records
                FROM ohlcv_data 
                WHERE strategy_id IS NOT NULL
                GROUP BY strategy_id, symbol, timeframe
                ORDER BY strategy_id, symbol, timeframe
            """)
            strategy_results = cursor.fetchall()
            
            print(f"\n📅 Earliest dates by strategy:")
            print("-" * 90)
            print(f"{'Strategy':<10} {'Symbol':<10} {'Timeframe':<10} {'Earliest Date':<20} {'Records'}")
            print("-" * 90)
            
            for row in strategy_results:
                earliest = row['earliest_date'].strftime('%Y-%m-%d %H:%M') if row['earliest_date'] else 'N/A'
                print(f"{row['strategy_id']:<10} {row['symbol']:<10} {row['timeframe']:<10} {earliest:<20} {row['total_records']}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error testing queries: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting database schema validation...")
    print("=" * 80)
    
    success = True
    
    # Validate ohlcv_data table
    if not validate_ohlcv_schema():
        success = False
    
    # Validate strategies table
    if not validate_strategies_table():
        success = False
    
    # Test queries
    if not test_earliest_date_query():
        success = False
    
    print("\n" + "=" * 80)
    if success:
        print("✅ Database schema validation completed successfully!")
    else:
        print("❌ Database schema validation failed!")
        sys.exit(1)
