/**
 * Monthly Chart Manager
 * Manages loading and displaying monthly data starting from the earliest available date
 */
class MonthlyChartManager {
    constructor() {
        this.currentYear = null;
        this.currentMonth = null;
        this.earliestYear = null;
        this.earliestMonth = null;
        this.symbol = 'BTCUSDT';
        this.timeframe = '1d';
        this.strategy_id = null;
        this.isLoading = false;
        this.hasMarkedCurrentMonth = false;
        
        this.initializeUI();
    }

    initializeUI() {
        // Create monthly navigation UI
        const container = document.getElementById('chart-controls') || document.body;
        
        const monthlyControls = document.createElement('div');
        monthlyControls.id = 'monthly-controls';
        monthlyControls.className = 'monthly-controls';
        monthlyControls.innerHTML = `
            <div class="monthly-nav">
                <h3>Monthly Data Navigation</h3>
                <div class="month-info">
                    <span id="current-month-display">Loading...</span>
                </div>
                <div class="month-controls">
                    <button id="load-earliest-month" class="btn btn-primary">Load Earliest Month</button>
                    <button id="next-month-btn" class="btn btn-secondary" disabled>Next Month</button>
                    <button id="mark-month-complete" class="btn btn-success" disabled>Mark Month Complete</button>
                </div>
                <div class="month-progress">
                    <div id="month-progress-info"></div>
                </div>
            </div>
        `;
        
        container.appendChild(monthlyControls);
        
        // Add event listeners
        this.setupEventListeners();
        
        // Add CSS styles
        this.addStyles();
    }

    setupEventListeners() {
        document.getElementById('load-earliest-month').addEventListener('click', () => {
            this.loadEarliestMonth();
        });
        
        document.getElementById('next-month-btn').addEventListener('click', () => {
            this.loadNextMonth();
        });
        
        document.getElementById('mark-month-complete').addEventListener('click', () => {
            this.markMonthComplete();
        });
    }

    addStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .monthly-controls {
                background: #1e222d;
                border: 1px solid #363c4e;
                border-radius: 8px;
                padding: 15px;
                margin: 10px 0;
                color: #d1d4dc;
            }
            
            .monthly-nav h3 {
                margin: 0 0 15px 0;
                color: #ffffff;
                font-size: 16px;
            }
            
            .month-info {
                margin-bottom: 15px;
                font-size: 14px;
                font-weight: bold;
                color: #4caf50;
            }
            
            .month-controls {
                display: flex;
                gap: 10px;
                margin-bottom: 15px;
                flex-wrap: wrap;
            }
            
            .month-controls .btn {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                transition: background-color 0.2s;
            }
            
            .btn-primary {
                background: #2196f3;
                color: white;
            }
            
            .btn-primary:hover:not(:disabled) {
                background: #1976d2;
            }
            
            .btn-secondary {
                background: #757575;
                color: white;
            }
            
            .btn-secondary:hover:not(:disabled) {
                background: #616161;
            }
            
            .btn-success {
                background: #4caf50;
                color: white;
            }
            
            .btn-success:hover:not(:disabled) {
                background: #388e3c;
            }
            
            .btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }
            
            .month-progress {
                font-size: 12px;
                color: #888;
            }
        `;
        document.head.appendChild(style);
    }

    async loadEarliestMonth() {
        if (this.isLoading) return;
        
        try {
            this.isLoading = true;
            this.updateStatus('Finding earliest available data...');
            
            // Get earliest date
            const earliestResponse = await fetch(`/api/v1/ohlcv/earliest-date?symbol=${this.symbol}&timeframe=${this.timeframe}${this.strategy_id ? `&strategy_id=${this.strategy_id}` : ''}`);
            const earliestResult = await earliestResponse.json();
            
            if (!earliestResult.success) {
                throw new Error('No data found');
            }
            
            this.earliestYear = earliestResult.data.year;
            this.earliestMonth = earliestResult.data.month;
            this.currentYear = this.earliestYear;
            this.currentMonth = this.earliestMonth;
            
            // Load the earliest month data
            await this.loadCurrentMonth();
            
            // Enable next month button
            document.getElementById('next-month-btn').disabled = false;
            document.getElementById('mark-month-complete').disabled = false;
            document.getElementById('load-earliest-month').disabled = true;
            
        } catch (error) {
            console.error('Error loading earliest month:', error);
            this.updateStatus('Error loading earliest month: ' + error.message, 'error');
        } finally {
            this.isLoading = false;
        }
    }

    async loadCurrentMonth() {
        if (!this.currentYear || !this.currentMonth) return;
        
        try {
            this.updateStatus(`Loading data for ${this.getMonthName(this.currentMonth)} ${this.currentYear}...`);
            
            // Load monthly data
            const response = await fetch(`/api/v1/ohlcv/monthly-data?symbol=${this.symbol}&timeframe=${this.timeframe}&year=${this.currentYear}&month=${this.currentMonth}${this.strategy_id ? `&strategy_id=${this.strategy_id}` : ''}`);
            const result = await response.json();
            
            if (!result.success) {
                throw new Error('No data found for this month');
            }
            
            // Update chart with monthly data
            this.updateChart(result.data.ohlcv);
            
            // Update UI
            this.updateMonthDisplay();
            this.updateProgressInfo(result.data.count);
            
            // Reset marking status for new month
            this.hasMarkedCurrentMonth = false;
            document.getElementById('mark-month-complete').disabled = false;
            document.getElementById('next-month-btn').disabled = true;
            
            this.updateStatus(`Loaded ${result.data.count} candles for ${this.getMonthName(this.currentMonth)} ${this.currentYear}`);
            
        } catch (error) {
            console.error('Error loading monthly data:', error);
            this.updateStatus('Error loading monthly data: ' + error.message, 'error');
        }
    }

    async loadNextMonth() {
        if (this.isLoading || !this.hasMarkedCurrentMonth) return;
        
        // Calculate next month
        let nextMonth = this.currentMonth + 1;
        let nextYear = this.currentYear;
        
        if (nextMonth > 12) {
            nextMonth = 1;
            nextYear++;
        }
        
        this.currentMonth = nextMonth;
        this.currentYear = nextYear;
        
        await this.loadCurrentMonth();
    }

    markMonthComplete() {
        if (this.hasMarkedCurrentMonth) return;
        
        this.hasMarkedCurrentMonth = true;
        document.getElementById('mark-month-complete').disabled = true;
        document.getElementById('next-month-btn').disabled = false;
        
        this.updateStatus(`Month ${this.getMonthName(this.currentMonth)} ${this.currentYear} marked as complete. You can now proceed to the next month.`, 'success');
    }

    updateChart(ohlcvData) {
        // Update the main chart with monthly data
        if (window.professionalChart && window.professionalChart.candlestickSeries) {
            const chartData = ohlcvData.map(candle => ({
                time: Math.floor(new Date(candle.timestamp).getTime() / 1000),
                open: parseFloat(candle.open),
                high: parseFloat(candle.high),
                low: parseFloat(candle.low),
                close: parseFloat(candle.close)
            }));
            
            const volumeData = ohlcvData.map(candle => ({
                time: Math.floor(new Date(candle.timestamp).getTime() / 1000),
                value: parseFloat(candle.volume),
                color: parseFloat(candle.close) >= parseFloat(candle.open) ? '#26a69a' : '#ef5350'
            }));
            
            window.professionalChart.candlestickSeries.setData(chartData);
            if (window.professionalChart.volumeSeries) {
                window.professionalChart.volumeSeries.setData(volumeData);
            }
            
            // Update indicators manager if available
            if (window.indicatorsManager) {
                window.indicatorsManager.setCurrentData(ohlcvData);
            }
            
            // Fit chart to content
            window.professionalChart.chart.timeScale().fitContent();
        }
    }

    updateMonthDisplay() {
        const display = document.getElementById('current-month-display');
        if (display) {
            display.textContent = `${this.getMonthName(this.currentMonth)} ${this.currentYear}`;
        }
    }

    updateProgressInfo(candleCount) {
        const info = document.getElementById('month-progress-info');
        if (info) {
            const monthsSinceStart = this.getMonthsSinceStart();
            info.textContent = `${candleCount} candles loaded | Month ${monthsSinceStart} since start (${this.getMonthName(this.earliestMonth)} ${this.earliestYear})`;
        }
    }

    updateStatus(message, type = 'info') {
        console.log(`[Monthly Chart] ${message}`);
        
        // Update status in UI if status element exists
        const statusElement = document.getElementById('chart-status') || document.getElementById('status');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = type;
        }
    }

    getMonthName(month) {
        const months = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
        ];
        return months[month - 1];
    }

    getMonthsSinceStart() {
        if (!this.earliestYear || !this.earliestMonth || !this.currentYear || !this.currentMonth) {
            return 0;
        }
        
        return (this.currentYear - this.earliestYear) * 12 + (this.currentMonth - this.earliestMonth) + 1;
    }

    // Public methods for external control
    setSymbol(symbol) {
        this.symbol = symbol;
    }

    setTimeframe(timeframe) {
        this.timeframe = timeframe;
    }

    setStrategyId(strategyId) {
        this.strategy_id = strategyId;
    }
}

// Global instance
window.monthlyChartManager = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.monthlyChartManager = new MonthlyChartManager();
});
