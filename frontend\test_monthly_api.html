<!DOCTYPE html>
<html>
<head>
    <title>Monthly API Test</title>
</head>
<body>
    <h1>Monthly Chart API Test</h1>
    <div id="results"></div>
    <button onclick="testEarliestDate()">Test Earliest Date API</button>
    <button onclick="testMonthlyData()">Test Monthly Data API</button>

    <script>
        async function testEarliestDate() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = 'Testing earliest date API...';
            
            try {
                const url = '/api/v1/ohlcv/earliest-date?symbol=BTCUSDT&timeframe=15m&strategy_id=1';
                console.log('Testing URL:', url);
                
                const response = await fetch(url);
                const result = await response.json();
                
                resultsDiv.innerHTML = `
                    <h3>Earliest Date API Test Result:</h3>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
                console.log('Earliest date result:', result);
            } catch (error) {
                resultsDiv.innerHTML = `
                    <h3>Earliest Date API Test Error:</h3>
                    <pre>${error.message}</pre>
                `;
                console.error('Error:', error);
            }
        }

        async function testMonthlyData() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = 'Testing monthly data API...';
            
            try {
                const url = '/api/v1/ohlcv/monthly-data?symbol=BTCUSDT&timeframe=15m&year=2023&month=1&strategy_id=1';
                console.log('Testing URL:', url);
                
                const response = await fetch(url);
                const result = await response.json();
                
                resultsDiv.innerHTML = `
                    <h3>Monthly Data API Test Result:</h3>
                    <p>Success: ${result.success}</p>
                    <p>Message: ${result.message}</p>
                    <p>Records Count: ${result.data ? result.data.count : 'N/A'}</p>
                    <pre>${JSON.stringify(result, null, 2).substring(0, 1000)}...</pre>
                `;
                console.log('Monthly data result:', result);
            } catch (error) {
                resultsDiv.innerHTML = `
                    <h3>Monthly Data API Test Error:</h3>
                    <pre>${error.message}</pre>
                `;
                console.error('Error:', error);
            }
        }

        // Test on page load
        window.addEventListener('load', () => {
            console.log('Page loaded, testing APIs...');
            testEarliestDate();
        });
    </script>
</body>
</html>
