// Chart management using TradingView Lightweight Charts

// Debug function to check TradingView library
function debugTradingViewLibrary() {
    console.log('=== TradingView Library Debug ===');
    console.log('LightweightCharts available:', typeof LightweightCharts !== 'undefined');

    if (typeof LightweightCharts !== 'undefined') {
        console.log('LightweightCharts object:', LightweightCharts);
        console.log('createChart method:', typeof LightweightCharts.createChart);

        // Try creating a temporary chart to test methods
        try {
            const tempDiv = document.createElement('div');
            tempDiv.style.width = '100px';
            tempDiv.style.height = '100px';
            document.body.appendChild(tempDiv);

            const tempChart = LightweightCharts.createChart(tempDiv, { width: 100, height: 100 });
            console.log('Temporary chart created:', tempChart);
            console.log('addCandlestickSeries method:', typeof tempChart.addCandlestickSeries);
            console.log('Available methods:', Object.getOwnPropertyNames(tempChart));

            tempChart.remove();
            document.body.removeChild(tempDiv);
        } catch (error) {
            console.error('Error testing temporary chart:', error);
        }
    }
    console.log('=== End Debug ===');
}

class ChartManager {
    constructor(containerId) {
        this.containerId = containerId;
        this.chart = null;
        this.candlestickSeries = null;
        this.volumeSeries = null;
        this.indicatorSeries = {};
        this.currentData = [];
        this.currentIndicators = {};
        this.markers = [];

        // Wait for library to load before initializing
        this.waitForLibraryAndInit();
    }

    waitForLibraryAndInit() {
        if (typeof LightweightCharts !== 'undefined') {
            console.log('TradingView library ready, initializing chart...');
            this.initChart();
        } else {
            console.log('Waiting for TradingView library to load...');
            setTimeout(() => this.waitForLibraryAndInit(), 100);
        }
    }
    
    initChart() {
        const container = document.getElementById(this.containerId);
        if (!container) {
            console.error(`Container with id '${this.containerId}' not found`);
            return;
        }

        // Check if TradingView library is loaded
        if (typeof LightweightCharts === 'undefined') {
            console.error('TradingView Lightweight Charts library not loaded');
            this.updateChartStatus('Error: Chart library not loaded');
            return;
        }

        try {
            this.chart = LightweightCharts.createChart(container, {
                width: container.clientWidth,
                height: 600,
                layout: {
                    backgroundColor: '#131722',
                    textColor: '#d1d4dc',
                    fontSize: 12,
                    fontFamily: 'Trebuchet MS, sans-serif',
                },
                grid: {
                    vertLines: {
                        color: '#363c4e',
                        style: 1,
                        visible: true,
                    },
                    horzLines: {
                        color: '#363c4e',
                        style: 1,
                        visible: true,
                    },
                },
                crosshair: {
                    mode: LightweightCharts.CrosshairMode.Normal,
                    vertLine: {
                        color: '#758696',
                        width: 1,
                        style: 3,
                        visible: true,
                        labelVisible: true,
                    },
                    horzLine: {
                        color: '#758696',
                        width: 1,
                        style: 3,
                        visible: true,
                        labelVisible: true,
                    },
                },
                rightPriceScale: {
                    borderColor: '#485c7b',
                    textColor: '#b2b5be',
                    entireTextOnly: false,
                    autoScale: true,
                    alignLabels: true,
                    borderVisible: true,
                    ticksVisible: true,
                    minimumWidth: 80,
                    scaleMargins: {
                        top: 0.1,
                        bottom: 0.1,
                    },
                    // Custom tick formatter for 250-point intervals
                    tickMarkFormatter: (price) => {
                        const interval = 250;
                        const rounded = Math.round(price / interval) * interval;

                        // Only show major ticks at 250-point intervals
                        if (Math.abs(price - rounded) < 125) { // Within half interval
                            if (rounded >= 1000) {
                                return `$${(rounded / 1000).toFixed(1)}K`;
                            } else {
                                return `$${rounded.toFixed(0)}`;
                            }
                        }
                        return ''; // Hide minor ticks
                    },
                },
                timeScale: {
                    borderColor: '#485c7b',
                    textColor: '#b2b5be',
                    timeVisible: true,
                    secondsVisible: false,
                    rightOffset: 12,
                    barSpacing: 8,
                    fixLeftEdge: false,
                    lockVisibleTimeRangeOnResize: true,
                    rightBarStaysOnScroll: true,
                    borderVisible: true,
                    ticksVisible: true,
                    // Custom tick formatter for 4-hour intervals
                    tickMarkFormatter: (time, tickMarkType, locale) => {
                        const date = new Date(time * 1000);
                        const hours = date.getHours();

                        // Show major ticks every 4 hours (0, 4, 8, 12, 16, 20)
                        if (hours % 4 === 0) {
                            if (tickMarkType === LightweightCharts.TickMarkType.Year) {
                                return date.getFullYear().toString();
                            } else if (tickMarkType === LightweightCharts.TickMarkType.Month) {
                                return date.toLocaleDateString(locale, { month: 'short' });
                            } else if (tickMarkType === LightweightCharts.TickMarkType.DayOfMonth) {
                                return date.getDate().toString();
                            } else if (tickMarkType === LightweightCharts.TickMarkType.Time) {
                                return date.toLocaleTimeString(locale, {
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    hour12: false
                                });
                            }
                        }
                        return ''; // Hide non-4-hour ticks
                    },
                },
                watermark: {
                    visible: false,
                },
            });

            // Create candlestick series - try different API methods
            let candlestickSeries = null;

            if (typeof this.chart.addCandlestickSeries === 'function') {
                // Standard API - TradingView colors
                candlestickSeries = this.chart.addCandlestickSeries({
                    upColor: '#26a69a',
                    downColor: '#ef5350',
                    borderDownColor: '#ef5350',
                    borderUpColor: '#26a69a',
                    wickDownColor: '#ef5350',
                    wickUpColor: '#26a69a',
                });
            } else if (typeof this.chart.addSeries === 'function') {
                // Alternative API - TradingView colors
                candlestickSeries = this.chart.addSeries('candlestick', {
                    upColor: '#26a69a',
                    downColor: '#ef5350',
                    borderDownColor: '#ef5350',
                    borderUpColor: '#26a69a',
                    wickDownColor: '#ef5350',
                    wickUpColor: '#26a69a',
                });
            } else {
                console.error('No candlestick series method available');
                this.updateChartStatus('Error: Chart API not compatible');
                return;
            }

            this.candlestickSeries = candlestickSeries;

            // Create volume series - try different API methods
            let volumeSeries = null;

            if (typeof this.chart.addHistogramSeries === 'function') {
                // Standard API - TradingView volume style
                volumeSeries = this.chart.addHistogramSeries({
                    color: '#26a69a',
                    priceFormat: {
                        type: 'volume',
                    },
                    priceScaleId: '',
                    scaleMargins: {
                        top: 0.7,
                        bottom: 0,
                    },
                });
            } else if (typeof this.chart.addSeries === 'function') {
                // Alternative API
                volumeSeries = this.chart.addSeries('histogram', {
                    color: '#26a69a',
                    priceFormat: {
                        type: 'volume',
                    },
                    priceScaleId: '',
                    scaleMargins: {
                        top: 0.8,
                        bottom: 0,
                    },
                });
            }

            this.volumeSeries = volumeSeries;
        
        // Handle crosshair move
        this.chart.subscribeCrosshairMove((param) => {
            this.updateCrosshairInfo(param);
        });
        
        // Handle click events for marking trades
        this.chart.subscribeClick((param) => {
            this.handleChartClick(param);
        });
        
            // Handle resize
            window.addEventListener('resize', () => {
                this.chart.applyOptions({ width: container.clientWidth });
            });

            this.updateChartStatus('Chart initialized successfully');

        } catch (error) {
            console.error('Error initializing chart:', error);
            this.updateChartStatus('Error: Failed to initialize chart');
        }
    }
    
    updateData(ohlcvData) {
        if (!ohlcvData || ohlcvData.length === 0) {
            console.error('No OHLCV data provided');
            this.updateChartStatus('No data to display');
            return;
        }

        if (!this.chart || !this.candlestickSeries || !this.volumeSeries) {
            console.error('Chart not properly initialized');
            this.updateChartStatus('Error: Chart not ready');
            return;
        }

        this.currentData = ohlcvData;
        
        // Convert data for chart (include volume for OHLCV extraction)
        const candleData = ohlcvData.map(item => ({
            time: new Date(item.timestamp).getTime() / 1000,
            open: parseFloat(item.open),
            high: parseFloat(item.high),
            low: parseFloat(item.low),
            close: parseFloat(item.close),
            volume: parseFloat(item.volume) // Include volume for marking tools
        }));

        const volumeData = ohlcvData.map(item => ({
            time: new Date(item.timestamp).getTime() / 1000,
            value: parseFloat(item.volume),
            color: parseFloat(item.close) >= parseFloat(item.open) ? '#26a69a' : '#ef5350'
        }));
        
        // Update series
        try {
            this.candlestickSeries.setData(candleData);
            this.volumeSeries.setData(volumeData);

            // Position chart to show earliest data instead of latest
            this.positionToEarliestData(candleData);

            this.updateChartStatus(`Loaded ${ohlcvData.length} candles`);
        } catch (error) {
            console.error('Error updating chart series:', error);
            this.updateChartStatus('Error updating chart');
        }
    }

    positionToEarliestData(candleData) {
        if (!this.chart || !candleData || candleData.length === 0) return;

        try {
            // Get the earliest timestamp from the data
            const earliestTime = candleData[0].time;
            const latestTime = candleData[candleData.length - 1].time;

            // Calculate a reasonable visible range starting from the earliest data
            // Show about 100-200 candles initially (adjust based on data length)
            const visibleCandles = Math.min(200, Math.max(50, candleData.length * 0.3));
            const timePerCandle = (latestTime - earliestTime) / (candleData.length - 1);
            const visibleTimeRange = visibleCandles * timePerCandle;

            // Set visible range to start from the earliest data
            this.chart.timeScale().setVisibleRange({
                from: earliestTime,
                to: earliestTime + visibleTimeRange
            });

            console.log(`Chart positioned to show earliest data from ${new Date(earliestTime * 1000).toISOString()}`);
        } catch (error) {
            console.error('Error positioning to earliest data:', error);
            // Fallback to fitContent if positioning fails
            this.chart.timeScale().fitContent();
        }
    }

    updateIndicators(indicators) {
        this.currentIndicators = indicators;
        
        // Clear existing indicator series
        Object.values(this.indicatorSeries).forEach(series => {
            this.chart.removeSeries(series);
        });
        this.indicatorSeries = {};
        
        if (!indicators.timestamps) {
            console.error('No timestamps in indicators data');
            return;
        }
        
        const timestamps = indicators.timestamps.map(ts => new Date(ts).getTime() / 1000);
        
        // Add EMA
        if (indicators.ema) {
            const emaData = timestamps.map((time, i) => ({
                time: time,
                value: indicators.ema[i]
            })).filter(item => item.value && !isNaN(item.value));
            
            this.indicatorSeries.ema = this.chart.addLineSeries({
                color: '#2962ff',
                lineWidth: 2,
                title: 'EMA',
                lineStyle: 0,
                crosshairMarkerVisible: true,
                crosshairMarkerRadius: 6,
            });
            this.indicatorSeries.ema.setData(emaData);
        }
        
        // Add SMA
        if (indicators.sma) {
            const smaData = timestamps.map((time, i) => ({
                time: time,
                value: indicators.sma[i]
            })).filter(item => item.value && !isNaN(item.value));
            
            this.indicatorSeries.sma = this.chart.addLineSeries({
                color: '#ff6d00',
                lineWidth: 2,
                title: 'SMA',
                lineStyle: 0,
                crosshairMarkerVisible: true,
                crosshairMarkerRadius: 6,
            });
            this.indicatorSeries.sma.setData(smaData);
        }
        
        // Add Bollinger Bands
        if (indicators.bollinger_bands) {
            const upperData = timestamps.map((time, i) => ({
                time: time,
                value: indicators.bollinger_bands.upper[i]
            })).filter(item => item.value && !isNaN(item.value));
            
            const lowerData = timestamps.map((time, i) => ({
                time: time,
                value: indicators.bollinger_bands.lower[i]
            })).filter(item => item.value && !isNaN(item.value));
            
            this.indicatorSeries.bbUpper = this.chart.addLineSeries({
                color: '#9c27b0',
                lineWidth: 1,
                title: 'BB Upper',
                lineStyle: 2, // Dashed line
                crosshairMarkerVisible: false,
            });
            this.indicatorSeries.bbUpper.setData(upperData);

            this.indicatorSeries.bbLower = this.chart.addLineSeries({
                color: '#9c27b0',
                lineWidth: 1,
                title: 'BB Lower',
                lineStyle: 2, // Dashed line
                crosshairMarkerVisible: false,
            });
            this.indicatorSeries.bbLower.setData(lowerData);
        }

        // Add RSI (if available) - TradingView style
        if (indicators.rsi) {
            const rsiData = timestamps.map((time, i) => ({
                time: time,
                value: indicators.rsi[i]
            })).filter(item => item.value && !isNaN(item.value));

            this.indicatorSeries.rsi = this.chart.addLineSeries({
                color: '#9c27b0',
                lineWidth: 2,
                title: 'RSI',
                lineStyle: 0,
                crosshairMarkerVisible: true,
                crosshairMarkerRadius: 6,
                priceScaleId: 'rsi',
            });
            this.indicatorSeries.rsi.setData(rsiData);
        }

        // Add MACD (if available) - TradingView style
        if (indicators.macd) {
            const macdData = timestamps.map((time, i) => ({
                time: time,
                value: indicators.macd.macd[i]
            })).filter(item => item.value && !isNaN(item.value));

            const signalData = timestamps.map((time, i) => ({
                time: time,
                value: indicators.macd.signal[i]
            })).filter(item => item.value && !isNaN(item.value));

            const histogramData = timestamps.map((time, i) => ({
                time: time,
                value: indicators.macd.histogram[i],
                color: indicators.macd.histogram[i] >= 0 ? '#26a69a' : '#ef5350'
            })).filter(item => item.value && !isNaN(item.value));

            this.indicatorSeries.macdLine = this.chart.addLineSeries({
                color: '#2962ff',
                lineWidth: 2,
                title: 'MACD',
                priceScaleId: 'macd',
            });
            this.indicatorSeries.macdLine.setData(macdData);

            this.indicatorSeries.macdSignal = this.chart.addLineSeries({
                color: '#ff6d00',
                lineWidth: 2,
                title: 'Signal',
                priceScaleId: 'macd',
            });
            this.indicatorSeries.macdSignal.setData(signalData);

            this.indicatorSeries.macdHistogram = this.chart.addHistogramSeries({
                priceScaleId: 'macd',
            });
            this.indicatorSeries.macdHistogram.setData(histogramData);
        }

        this.updateChartStatus('Indicators updated');
    }
    
    addTradeMarker(timestamp, type, side, price) {
        const time = new Date(timestamp).getTime() / 1000;
        
        const marker = {
            time: time,
            position: type === 'entry' ? 'belowBar' : 'aboveBar',
            color: type === 'entry' ? (side === 'buy' ? '#26a69a' : '#ef5350') : '#ff6d00',
            shape: type === 'entry' ? 'arrowUp' : 'arrowDown',
            text: `${type.toUpperCase()} ${side ? side.toUpperCase() : ''} @ ${price.toFixed(4)}`
        };
        
        this.markers.push(marker);
        this.candlestickSeries.setMarkers(this.markers);
        
        return marker;
    }
    
    clearMarkers() {
        this.markers = [];
        this.candlestickSeries.setMarkers([]);
    }
    
    updateCrosshairInfo(param) {
        const crosshairInfo = document.getElementById('crosshair-info');
        
        if (!param.time || !param.point) {
            crosshairInfo.textContent = '';
            return;
        }
        
        const data = param.seriesPrices.get(this.candlestickSeries);
        if (data) {
            const date = new Date(param.time * 1000);
            crosshairInfo.textContent = `${date.toLocaleString()} | O: ${data.open?.toFixed(4)} H: ${data.high?.toFixed(4)} L: ${data.low?.toFixed(4)} C: ${data.close?.toFixed(4)}`;
        }
    }
    
    handleChartClick(param) {
        if (!param.time || !param.point) return;
        
        const data = param.seriesPrices.get(this.candlestickSeries);
        if (!data) return;
        
        // Emit custom event for trade marking
        const event = new CustomEvent('chartClick', {
            detail: {
                timestamp: new Date(param.time * 1000).toISOString(),
                price: data.close,
                ohlcv: data
            }
        });
        document.dispatchEvent(event);
    }
    
    updateChartStatus(message) {
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.textContent = message;
        }
    }
    
    destroy() {
        if (this.chart) {
            this.chart.remove();
        }
    }
}

// Global chart instance
let chartManager = null;

// Initialize chart when DOM is loaded and TradingView library is ready
document.addEventListener('DOMContentLoaded', () => {
    // Wait a bit for the TradingView library to fully load
    const initChart = () => {
        if (typeof LightweightCharts !== 'undefined') {
            debugTradingViewLibrary(); // Debug the library
            chartManager = new ChartManager('chart');
        } else {
            console.log('Waiting for TradingView library to load...');
            setTimeout(initChart, 100);
        }
    };

    initChart();
});
