/**
 * Marking Tools for TradingView Chart
 * Handles entry/exit marking with database integration
 */

class MarkingTools {
    constructor(chart) {
        this.chart = chart;
        this.marks = new Map(); // Store marks by ID
        this.tradeLines = new Map(); // Store line series for entry-exit connections
        this.currentClickData = null;
        this.currentModalType = null; // Track current modal type ('entry' or 'exit')
        this.currentTradeData = null; // Store extracted trade data for form submission
        this.isMarkingMode = false;

        // Drag and drop functionality
        this.isDragging = false;
        this.draggedMark = null;
        this.dragStartPosition = null;
        this.dragGhostLine = null;
        this.dragFeedbackElements = [];

        this.initializeEventListeners();
        this.initializeDragAndDrop();
        this.loadExistingMarks();
    }

    initializeEventListeners() {
        // Chart click handlers using TradingView's built-in events
        if (this.chart && this.chart.chart) {
            console.log('Setting up chart click handlers for marking tools');

            // Use TradingView's click event
            this.chart.chart.subscribeClick((param) => {
                console.log('Chart clicked via TradingView API, marking mode:', this.isMarkingMode);
                console.log('Click param:', param);

                if (this.isMarkingMode && param.point) {
                    console.log('Processing chart click for entry marking');
                    this.handleTradingViewClick(param, 'entry');
                }
            });

            // Right-click functionality disabled - using dropdown selection instead
            if (this.chart.container) {
                this.chart.container.addEventListener('contextmenu', (e) => {
                    e.preventDefault();
                    console.log('Right-click disabled - use dropdown in modal to select entry/exit');
                    // No longer trigger exit modal on right-click
                });
            }

            console.log('Chart click handlers set up successfully');
        } else {
            console.warn('Chart not ready for marking tools event listeners');
            console.log('Chart:', this.chart);
            console.log('Chart.chart:', this.chart?.chart);
            console.log('Chart.container:', this.chart?.container);
        }

        // Modal event listeners
        this.initializeModalListeners();

        // Sidebar event listeners
        this.initializeSidebarListeners();
    }

    initializeModalListeners() {
        // Unified trade modal
        const tradeModal = document.getElementById('trade-modal');
        const tradeClose = document.getElementById('trade-modal-close');
        const tradeCancel = document.getElementById('trade-cancel');
        const tradeConfirm = document.getElementById('trade-confirm');

        if (tradeClose) {
            tradeClose.addEventListener('click', () => this.closeModal('trade-modal'));
        }

        if (tradeCancel) {
            tradeCancel.addEventListener('click', () => this.closeModal('trade-modal'));
        }

        if (tradeConfirm) {
            tradeConfirm.addEventListener('click', () => this.confirmTrade());
        }

        // Trade type dropdown change listener
        const tradeTypeSelect = document.getElementById('trade-type');
        if (tradeTypeSelect) {
            tradeTypeSelect.addEventListener('change', (e) => {
                const selectedType = e.target.value;
                console.log(`🔄 Trade type changed to: ${selectedType}`);
                this.configureFormForTradeType(selectedType);
            });
        }

        // Close modals when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target.id);
            }
        });
    }

    initializeSidebarListeners() {
        // Export marks
        document.getElementById('export-marks').addEventListener('click', () => {
            this.exportMarks();
        });

        // Clear all marks
        document.getElementById('clear-all-marks').addEventListener('click', () => {
            this.clearAllMarks();
        });
    }

    handleTradingViewClick(param, type) {
        console.log(`handleTradingViewClick called with type: ${type}`);
        console.log('TradingView param:', param);

        if (!param.point || !param.time) {
            console.warn('Invalid click parameter from TradingView');
            return;
        }

        // Extract candlestick data from TradingView seriesData Map
        let candlestickData = null;
        let price = null;

        console.log('Param seriesData:', param.seriesData);

        if (param.seriesData && param.seriesData instanceof Map) {
            // Iterate through the Map to find candlestick data
            for (const [key, value] of param.seriesData) {
                console.log('SeriesData entry - Key:', key, 'Value:', value);

                // Check if this looks like candlestick data
                if (value && typeof value === 'object' &&
                    value.open !== undefined && value.high !== undefined &&
                    value.low !== undefined && value.close !== undefined) {

                    candlestickData = {
                        time: value.time || param.time,
                        open: value.open,
                        high: value.high,
                        low: value.low,
                        close: value.close,
                        volume: value.volume || 0
                    };

                    price = value.close;
                    console.log('✅ Found candlestick data from seriesData:', candlestickData);
                    break;
                }
            }
        }

        // Fallback: try other methods if seriesData didn't work
        if (!candlestickData) {
            console.log('No data in seriesData, trying other methods...');
            candlestickData = this.getCandlestickAtTime(param.time);

            if (candlestickData) {
                price = candlestickData.close;
            } else {
                // Final fallback: use coordinate-based price
                console.warn('Could not find candlestick data, using coordinate-based price');
                try {
                    const priceScale = this.chart.chart.priceScale('right');
                    price = priceScale.coordinateToPrice(param.point.y);
                } catch (error) {
                    console.warn('Could not get price from coordinates:', error);
                    price = 50000; // Default fallback
                }
            }
        }

        console.log(`Found candlestick:`, candlestickData);
        console.log(`Using candlestick close price: ${price} for time: ${param.time}`);

        this.currentClickData = {
            time: param.time,
            price: price,
            x: param.point.x,
            y: param.point.y,
            candlestickData: candlestickData // Use consistent property name
        };

        console.log('Current click data from TradingView:', this.currentClickData);

        // Always show the same modal, but pre-select the trade type
        console.log(`Showing trade modal with pre-selected type: ${type}`);
        this.showTradeModal(type);
    }

    handleChartClick(event, type) {
        console.log(`handleChartClick called with type: ${type}`);
        console.log('Event:', event);
        console.log('Chart container:', this.chart.container);

        const rect = this.chart.container.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        console.log(`Click coordinates: x=${x}, y=${y}`);

        // Get time from coordinates
        let time;
        try {
            time = this.chart.chart.timeScale().coordinateToTime(x);
            console.log(`Converted time: ${time}`);
        } catch (error) {
            console.warn('Could not get time from coordinates:', error);
            time = Math.floor(Date.now() / 1000);
        }

        // Get the exact candlestick data for the clicked time
        const candlestickData = this.getCandlestickAtTime(time);

        let price;
        if (candlestickData) {
            // Use the candlestick's close price for accuracy
            price = candlestickData.close;
        } else {
            // Fallback: use coordinate-based price
            console.warn('Could not find candlestick data, using coordinate-based price');
            try {
                const priceScale = this.chart.chart.priceScale('right');
                price = priceScale.coordinateToPrice(y);
            } catch (error) {
                console.warn('Could not get price from coordinates:', error);
                price = 50000; // Default fallback
            }
        }

        console.log(`Using candlestick data:`, candlestickData);

        this.currentClickData = {
            time: candlestickData ? candlestickData.time : time, // Use candlestick time if available, otherwise use coordinate time
            price: price,
            x: x,
            y: y,
            candlestickData: candlestickData // Use consistent property name
        };

        console.log('Current click data:', this.currentClickData);

        // Always show the same modal, but pre-select the trade type
        console.log(`Showing trade modal with pre-selected type: ${type}`);
        this.showTradeModal(type);
    }

    async showTradeModal(preselectedType = 'entry') {
        console.log(`🚀 showTradeModal called with preselected type: ${preselectedType}`);
        console.log('🔍 Current click data:', this.currentClickData);

        const modal = document.getElementById('trade-modal');

        if (!modal) {
            console.error('❌ Trade modal not found!');
            return;
        }

        // Validate click data
        if (!this.currentClickData) {
            console.error('❌ No click data available for trade modal');
            alert('Error: No click data available. Please click on the chart first.');
            return;
        }

        if (!this.currentClickData.time) {
            console.error('❌ No timestamp in click data:', this.currentClickData);
            alert('Error: Invalid click data - missing timestamp.');
            return;
        }

        if (!this.currentClickData.price) {
            console.error('❌ No price in click data:', this.currentClickData);
            alert('Error: Invalid click data - missing price.');
            return;
        }

        // Get current market data
        const symbol = this.getCurrentSymbol();
        const timeframe = this.getCurrentTimeframe();

        // Extract real price from candlestick data if available, otherwise use click price
        let actualPrice = this.currentClickData.price;
        let actualTime = this.currentClickData.time;

        // If we have candlestick data, use the close price for accuracy
        if (this.currentClickData.candlestickData) {
            console.log(`✅ Trade modal - Using candlestick data for accurate price:`, this.currentClickData.candlestickData);
            actualPrice = this.currentClickData.candlestickData.close;
            actualTime = this.currentClickData.candlestickData.time;
        } else {
            console.log(`⚠️  Trade modal - No candlestick data available, using click price:`, actualPrice);
        }

        const date = new Date(actualTime * 1000);
        const formattedTime = date.toLocaleString();
        const formattedPrice = `$${actualPrice.toFixed(2)}`;

        console.log(`📊 Trade modal market data:`, {
            symbol,
            timeframe,
            time: actualTime,
            price: actualPrice,
            hasCandlestickData: !!this.currentClickData.candlestickData
        });

        // Ensure timestamp is properly aligned with timeframe
        const alignedTimestamp = this.alignTimestampWithChart(actualTime);
        const alignedDate = new Date(alignedTimestamp * 1000);
        const alignedFormattedTime = alignedDate.toLocaleString();

        // Store the extracted trade data for form submission
        this.currentTradeData = {
            symbol: symbol,
            timeframe: timeframe,
            timestamp: alignedTimestamp,
            originalTimestamp: actualTime, // Keep original for debugging
            price: actualPrice,
            formattedTime: alignedFormattedTime,
            originalFormattedTime: formattedTime, // Keep original for debugging
            formattedPrice: formattedPrice,
            candlestickData: this.currentClickData.candlestickData
        };

        console.log(`💾 Stored trade data:`, this.currentTradeData);

        // Populate market data with real values
        this.setElementText('trade-symbol', symbol);
        this.setElementText('trade-timeframe', timeframe);
        this.setElementText('trade-time', formattedTime);
        this.setElementTextWithHover('trade-price', formattedPrice, this.createPriceTooltip(actualPrice));

        // Set the preselected trade type in dropdown
        const tradeTypeSelect = document.getElementById('trade-type');
        if (tradeTypeSelect) {
            tradeTypeSelect.value = preselectedType;
            console.log(`📋 Preselected trade type: ${preselectedType}`);
        }

        // Get and display OHLCV data using actual timestamp
        console.log(`🔍 Trade modal - extracting OHLCV data for timestamp:`, actualTime);
        const ohlcvData = await this.getOHLCVData(actualTime);
        console.log(`📈 Trade modal - OHLCV extraction result:`, ohlcvData);

        if (ohlcvData) {
            this.populateOHLCVData('trade-ohlcv', ohlcvData);
            // Show OHLCV section
            const ohlcvContainer = document.getElementById('trade-ohlcv');
            const ohlcvSection = ohlcvContainer ? ohlcvContainer.closest('.ohlcv-section') : null;
            if (ohlcvSection) {
                ohlcvSection.style.display = 'block';
            }
        } else {
            // Hide OHLCV section if no data available
            const ohlcvContainer = document.getElementById('trade-ohlcv');
            const ohlcvSection = ohlcvContainer ? ohlcvContainer.closest('.ohlcv-section') : null;
            if (ohlcvSection) {
                ohlcvSection.style.display = 'none';
            }
        }

        // Get and display indicator data using actual timestamp
        console.log(`🔍 Trade modal - extracting indicator data for timestamp:`, actualTime);
        const indicatorData = await this.getIndicatorData(actualTime);
        console.log(`📊 Trade modal - indicator data result:`, indicatorData);

        if (indicatorData) {
            this.populateIndicatorData('trade-indicators', indicatorData);
        } else {
            this.populateIndicatorData('trade-indicators', null);
        }

        // Configure form sections based on preselected type
        this.configureFormForTradeType(preselectedType);

        // Show the modal
        modal.style.display = 'block';
        console.log(`✅ Trade modal populated and displayed with preselected type: ${preselectedType}`);
    }

    configureFormForTradeType(tradeType) {
        console.log(`🔧 Configuring form for trade type: ${tradeType}`);

        const entryForm = document.getElementById('trade-entry-form');
        const exitForm = document.getElementById('trade-exit-form');
        const pnlSection = document.getElementById('trade-pnl-section');
        const confirmButton = document.getElementById('trade-confirm');

        if (tradeType === 'exit') {
            // Show exit form and P&L section
            if (exitForm) exitForm.style.display = 'block';
            if (pnlSection) pnlSection.style.display = 'block';

            // Update button text
            if (confirmButton) confirmButton.textContent = 'Add Exit';

            // Populate open entries for exit
            const entrySelect = document.getElementById('trade-entry-select');
            if (entrySelect) {
                this.populateOpenEntries(entrySelect);

                // Set up P&L calculation when entry is selected
                entrySelect.addEventListener('change', () => {
                    this.updatePnLCalculation();
                });
            }
        } else {
            // Hide exit form and P&L section for entry
            if (exitForm) exitForm.style.display = 'none';
            if (pnlSection) pnlSection.style.display = 'none';

            // Update button text
            if (confirmButton) confirmButton.textContent = 'Add Entry';

            // Reset entry form
            const sideSelect = document.getElementById('trade-side');
            const quantityInput = document.getElementById('trade-quantity');
            const notesInput = document.getElementById('trade-notes');

            if (sideSelect) sideSelect.value = 'buy';
            if (quantityInput) quantityInput.value = '';
            if (notesInput) notesInput.value = '';
        }

        console.log(`✅ Form configured for ${tradeType} type`);
    }

    populateOpenEntries(selectElement) {
        // Clear existing options except the first one
        selectElement.innerHTML = '<option value="">Select an open entry...</option>';

        // Add only entry marks that don't have exits yet
        console.log('🔍 Populating open entries, total marks:', this.marks.size);
        console.log('📊 All marks in collection:');
        this.marks.forEach((mark, id) => {
            console.log(`   Mark ${id}:`, {
                mark_type: mark.mark_type,
                side: mark.side,
                entry_side: mark.entry_side,
                status: mark.status,
                exit_timestamp: mark.exit_timestamp,
                exit_price: mark.exit_price,
                linked_trade_id: mark.linked_trade_id
            });
        });

        let entriesFound = 0;
        let entriesWithExits = 0;
        let entriesAdded = 0;

        this.marks.forEach((mark, id) => {
            console.log('📋 Checking mark:', id, mark);

            // Check if this is an entry mark
            const isEntryMark = mark.mark_type === 'ENTRY' ||
                               (!mark.mark_type && (mark.side || mark.entry_side)); // Legacy format

            if (isEntryMark) {
                entriesFound++;
            }

            // Check if this entry already has an exit
            const hasExit = mark.exit_timestamp ||
                           mark.exit_price ||
                           mark.status === 'closed' ||
                           mark.linked_trade_id;

            if (isEntryMark && hasExit) {
                entriesWithExits++;
                console.log(`🚫 Entry ${id} has exit:`, {
                    exit_timestamp: mark.exit_timestamp,
                    exit_price: mark.exit_price,
                    status: mark.status,
                    linked_trade_id: mark.linked_trade_id
                });
            }

            // Only show entries that don't have exits
            if (isEntryMark && !hasExit) {
                let side = mark.side || (mark.entry_side ? mark.entry_side.toLowerCase() : null) || 'buy';

                // Ensure side is a valid string before calling toUpperCase
                if (!side || typeof side !== 'string') {
                    side = 'buy'; // Default fallback
                }

                const entryPrice = mark.entry_price || mark.price || 0;
                const quantity = mark.quantity || 1; // Default quantity for new schema
                const timestamp = mark.entry_timestamp || mark.timestamp;
                const dateStr = timestamp ? new Date(timestamp * 1000).toLocaleString() : 'Unknown';

                const option = document.createElement('option');
                option.value = id;
                option.textContent = `${side.toUpperCase()} @ $${entryPrice.toFixed(2)} (${quantity}) - ${dateStr}`;
                selectElement.appendChild(option);

                entriesAdded++;
                console.log('✅ Added open entry option:', option.textContent);
            } else {
                const reason = !isEntryMark ? 'not an entry mark' : 'already has exit';
                console.log(`⏭️  Skipping mark (${reason}):`, mark);
            }
        });

        console.log('📊 Entry filtering summary:', {
            totalMarks: this.marks.size,
            entriesFound: entriesFound,
            entriesWithExits: entriesWithExits,
            entriesAdded: entriesAdded,
            dropdownOptions: selectElement.children.length - 1
        });
    }

    async confirmTrade() {
        // Get the selected trade type from dropdown
        const tradeTypeSelect = document.getElementById('trade-type');
        const selectedType = tradeTypeSelect ? tradeTypeSelect.value : 'entry';

        console.log(`🔄 confirmTrade called for selected type: ${selectedType}`);

        if (selectedType === 'entry') {
            await this.confirmEntry();
        } else if (selectedType === 'exit') {
            await this.confirmExit();
        } else {
            console.error('❌ Unknown trade type:', selectedType);
            alert('Error: Unknown trade type');
        }
    }

    async confirmEntry() {
        const side = document.getElementById('trade-side').value;
        const quantity = parseFloat(document.getElementById('trade-quantity').value);
        const notes = document.getElementById('trade-notes').value;

        // Validate side field
        if (!side || (side !== 'buy' && side !== 'sell')) {
            alert('Please select a valid entry side (buy or sell)');
            return;
        }

        if (!quantity || quantity <= 0) {
            alert('Please enter a valid quantity');
            return;
        }

        // Validate stored trade data
        if (!this.currentTradeData) {
            console.error('❌ No stored trade data available');
            alert('Error: Trade data not available. Please close and reopen the modal.');
            return;
        }

        // Get comprehensive market data using stored timestamp
        const symbol = this.currentTradeData.symbol;
        const timeframe = this.currentTradeData.timeframe;
        const ohlcvData = await this.getOHLCVData(this.currentTradeData.timestamp);
        const indicatorData = await this.getIndicatorData(this.currentTradeData.timestamp);

        const entryData = {
            timestamp: this.currentTradeData.timestamp,
            price: this.currentTradeData.price,
            side: side.toLowerCase(), // Ensure consistent lowercase format
            quantity: quantity,
            notes: notes,
            ohlcv_data: ohlcvData,
            indicator_data: indicatorData
        };

        // Store comprehensive data for local use
        const comprehensiveData = {
            symbol: symbol,
            timeframe: timeframe,
            entry: {
                timestamp: new Date(this.currentTradeData.timestamp * 1000).toISOString(),
                entry_side: side ? side.charAt(0).toUpperCase() + side.slice(1) : 'Buy', // Safe toUpperCase with fallback
                price: this.currentTradeData.price,
                ohlcv: ohlcvData,
                indicators: indicatorData
            }
        };

        console.log('Comprehensive entry data:', comprehensiveData);
        console.log('Entry data being sent to server:', entryData);
        console.log('📊 OHLCV data being sent:', entryData.ohlcv_data);
        console.log('📊 Volume in OHLCV data:', entryData.ohlcv_data?.volume);
        console.log('Timestamp details:', {
            storedTime: this.currentTradeData.timestamp,
            timestampType: typeof this.currentTradeData.timestamp,
            dateFromTimestamp: new Date(this.currentTradeData.timestamp * 1000),
            candlestickTime: this.currentTradeData.candlestickData?.time
        });

        try {
            const response = await fetch('/api/v1/marks/entry', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(entryData)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Server error response:', errorText);
                throw new Error(`Server error: ${response.status} - ${errorText}`);
            }

            const result = await response.json();

            if (result.success) {
                // Store comprehensive data with the mark
                result.data.comprehensiveData = comprehensiveData;

                // Ensure backward compatibility properties for entry selection
                result.data.status = 'open';
                result.data.side = entryData.side;
                result.data.quantity = entryData.quantity;
                result.data.notes = entryData.notes;

                // Add mark to chart
                this.addMarkToChart(result.data);

                // Update sidebar
                this.updateSidebar();

                this.closeModal('trade-modal');
                console.log('Entry mark added successfully with comprehensive data');
            } else {
                alert('Error adding entry mark: ' + result.message);
            }
        } catch (error) {
            console.error('Error adding entry mark:', error);
            alert('Error adding entry mark: ' + error.message);

            // For now, simulate successful entry for testing using new schema
            console.log('Simulating entry mark for testing...');
            const simulatedData = {
                id: Date.now(),
                symbol: symbol,
                timeframe: timeframe,
                mark_type: 'ENTRY',
                entry_side: entryData.side ? entryData.side.toUpperCase() : 'BUY', // Safe toUpperCase with fallback
                timestamp: entryData.timestamp,
                price: entryData.price,
                quantity: entryData.quantity,
                notes: entryData.notes,
                indicator_snapshot: entryData.indicator_data,
                ohlcv_snapshot: entryData.ohlcv_data,
                status: 'open', // Add status for backward compatibility
                side: entryData.side, // Add side for backward compatibility
                comprehensiveData: comprehensiveData
            };

            this.addMarkToChart(simulatedData);
            this.updateSidebar();
            this.closeModal('trade-modal');
        }
    }

    async confirmExit() {
        const entryId = document.getElementById('trade-entry-select').value;
        const quantity = parseFloat(document.getElementById('trade-exit-quantity').value);
        const notes = document.getElementById('trade-notes').value;

        if (!entryId) {
            alert('Please select an entry to exit');
            return;
        }

        if (!quantity || quantity <= 0) {
            alert('Please enter a valid exit quantity');
            return;
        }

        // Get the selected entry for comprehensive data
        console.log('🔍 Looking for entry with ID:', entryId, 'Type:', typeof entryId);
        console.log('📋 Available marks:', Array.from(this.marks.keys()));

        // Try to get the entry, handling both string and number IDs
        let selectedEntry = this.marks.get(entryId);
        if (!selectedEntry && typeof entryId === 'string') {
            // Try converting to number
            const numericId = parseInt(entryId);
            selectedEntry = this.marks.get(numericId);
            console.log('🔄 Tried numeric ID:', numericId, 'Found:', !!selectedEntry);
        }
        if (!selectedEntry && typeof entryId === 'number') {
            // Try converting to string
            const stringId = entryId.toString();
            selectedEntry = this.marks.get(stringId);
            console.log('🔄 Tried string ID:', stringId, 'Found:', !!selectedEntry);
        }

        if (!selectedEntry) {
            console.error('❌ Selected entry not found. Available entries:');
            this.marks.forEach((mark, id) => {
                console.log(`   - ID: ${id} (${typeof id}), Type: ${mark.mark_type}, Side: ${mark.entry_side || mark.side}`);
            });
            alert('Selected entry not found. Please try selecting an entry again.');
            return;
        }

        console.log('✅ Found selected entry:', selectedEntry);

        // Validate stored trade data
        if (!this.currentTradeData) {
            console.error('❌ No stored trade data available for exit');
            alert('Error: Trade data not available. Please close and reopen the modal.');
            return;
        }

        // Get comprehensive market data using stored data
        const symbol = this.currentTradeData.symbol;
        const timeframe = this.currentTradeData.timeframe;

        // Validate timestamp
        if (!this.currentTradeData.timestamp || isNaN(this.currentTradeData.timestamp)) {
            console.error('❌ Invalid timestamp in stored trade data:', this.currentTradeData.timestamp);
            alert('Invalid timestamp data. Please try clicking on the chart again.');
            return;
        }

        // Validate price
        if (!this.currentTradeData.price || isNaN(this.currentTradeData.price)) {
            console.error('❌ Invalid price in stored trade data:', this.currentTradeData.price);
            alert('Invalid price data. Please try clicking on the chart again.');
            return;
        }

        console.log('✅ Exit mark - using stored trade data:', this.currentTradeData);
        console.log('Exit mark - extracting OHLCV data for timestamp:', this.currentTradeData.timestamp);
        const ohlcvData = await this.getOHLCVData(this.currentTradeData.timestamp);
        console.log('Exit mark - OHLCV data extracted:', ohlcvData);

        if (!ohlcvData) {
            console.warn('⚠️  No OHLCV data available for exit mark');
        }

        console.log('Exit mark - extracting indicator data for timestamp:', this.currentTradeData.timestamp);
        const indicatorData = await this.getIndicatorData(this.currentTradeData.timestamp);
        console.log('Exit mark - Indicator data extracted:', indicatorData);

        if (!indicatorData) {
            console.warn('⚠️  No indicator data available for exit mark');
        }

        // Validate that we have at least some market data
        if (!ohlcvData && !indicatorData) {
            const proceed = confirm(
                'No market data (OHLCV or indicators) could be extracted for this exit mark. ' +
                'This may be due to missing chart data at the selected timestamp. ' +
                'Do you want to proceed anyway?'
            );
            if (!proceed) {
                return;
            }
        }

        const exitData = {
            entry_id: parseInt(entryId),
            timestamp: this.currentTradeData.timestamp,
            price: this.currentTradeData.price,
            quantity: quantity,
            notes: notes,
            ohlcv_data: ohlcvData,
            indicator_data: indicatorData
        };

        console.log('Exit data being sent to server:', exitData);

        // Handle both old and new schema formats for P&L calculation
        const entryPrice = selectedEntry.entry_price || selectedEntry.price || 0;
        let side = selectedEntry.side || (selectedEntry.entry_side ? selectedEntry.entry_side.toLowerCase() : null) || 'buy';

        // Ensure side is a valid string
        if (!side || typeof side !== 'string') {
            side = 'buy'; // Default fallback
        }

        const entryTimestamp = selectedEntry.entry_timestamp || selectedEntry.timestamp;

        // Calculate P&L using stored trade data
        let priceDiff = this.currentTradeData.price - entryPrice;
        if (side === 'sell') {
            priceDiff = -priceDiff;
        }
        const profitPct = (priceDiff / entryPrice) * 100;

        // Store comprehensive data for local use with enhanced validation
        const comprehensiveData = {
            symbol: symbol,
            timeframe: timeframe,
            entry: selectedEntry.comprehensiveData?.entry || {
                timestamp: new Date(entryTimestamp * 1000).toISOString(),
                entry_side: side && typeof side === 'string' ? side.charAt(0).toUpperCase() + side.slice(1) : 'Buy', // Safe toUpperCase
                price: entryPrice,
                ohlcv: this.parseOHLCVData(selectedEntry.entry_ohlcv_data || selectedEntry.ohlcv_snapshot),
                indicators: this.parseIndicatorData(selectedEntry.entry_indicator_data || selectedEntry.indicator_snapshot)
            },
            exit: {
                timestamp: new Date(this.currentTradeData.timestamp * 1000).toISOString(),
                unix_timestamp: this.currentTradeData.timestamp,
                price: this.currentTradeData.price,
                ohlcv: ohlcvData,
                indicators: indicatorData,
                data_quality: {
                    ohlcv_available: !!ohlcvData,
                    indicators_available: !!indicatorData,
                    timestamp_valid: !isNaN(this.currentTradeData.timestamp),
                    price_valid: !isNaN(this.currentTradeData.price)
                }
            },
            pnl: {
                absolute: priceDiff * quantity,
                percentage: profitPct,
                quantity: quantity,
                entry_price: entryPrice,
                exit_price: this.currentTradeData.price,
                side: side
            }
        };

        console.log('Comprehensive exit data with validation:', comprehensiveData);

        try {
            const response = await fetch('/api/v1/marks/exit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(exitData)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Server error response:', errorText);
                throw new Error(`Server error: ${response.status} - ${errorText}`);
            }

            const result = await response.json();
            console.log('Exit mark API response:', result);

            if (result.success) {
                // Store comprehensive data with the mark
                result.data.comprehensiveData = comprehensiveData;

                // Add the new EXIT mark to the marks collection
                const exitMark = {
                    id: result.data.id,
                    symbol: result.data.symbol,
                    timeframe: result.data.timeframe,
                    mark_type: 'EXIT',
                    entry_side: null,
                    timestamp: result.data.timestamp,
                    price: result.data.price,
                    quantity: quantity,
                    notes: notes,
                    indicator_snapshot: result.data.indicator_snapshot,
                    ohlcv_snapshot: result.data.ohlcv_snapshot,
                    linked_trade_id: result.data.linked_trade_id,
                    comprehensiveData: comprehensiveData
                };

                // Store the EXIT mark in the marks collection
                this.marks.set(result.data.id, exitMark);
                console.log('✅ Added EXIT mark to collection:', exitMark);

                // Update the original entry mark with exit data for backward compatibility
                const originalEntry = this.marks.get(parseInt(entryId));
                if (originalEntry) {
                    // Add exit data to the original entry mark
                    originalEntry.exit_timestamp = this.currentTradeData.timestamp;
                    originalEntry.exit_price = this.currentTradeData.price;
                    originalEntry.exit_quantity = quantity;
                    originalEntry.exit_notes = notes;
                    originalEntry.exit_ohlcv_data = ohlcvData;
                    originalEntry.exit_indicator_data = indicatorData;
                    originalEntry.status = 'closed';

                    // Update the stored mark
                    this.marks.set(parseInt(entryId), originalEntry);

                    console.log('✅ Updated entry mark with exit data:', originalEntry);
                } else {
                    console.warn('⚠️ Original entry mark not found for exit update');
                }

                // Add the EXIT mark to the chart
                this.addMarkToChart(exitMark);

                // Ensure exits are properly linked to entries
                this.linkExitsToEntries();

                // Refresh chart markers to show updated entry with exit
                this.refreshChartMarkers();

                // Update sidebar
                this.updateSidebar();

                this.closeModal('trade-modal');
                console.log('Exit mark added successfully with comprehensive data');

                // Log data extraction success
                console.log('✅ Exit mark data extraction completed:', {
                    ohlcv_extracted: !!ohlcvData,
                    indicators_extracted: !!indicatorData,
                    timestamp_valid: !isNaN(this.currentTradeData.timestamp),
                    price_valid: !isNaN(this.currentTradeData.price)
                });
            } else {
                console.error('Exit mark creation failed:', result);
                alert('Error adding exit mark: ' + (result.message || 'Unknown error'));
            }
        } catch (error) {
            console.error('Error adding exit mark:', error);
            alert('Error adding exit mark: ' + error.message);

            // Log data extraction failure details
            console.error('❌ Exit mark data extraction failed:', {
                error: error.message,
                ohlcv_data: ohlcvData,
                indicator_data: indicatorData,
                timestamp: this.currentTradeData.timestamp,
                price: this.currentTradeData.price
            });
        }
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'none';
        }
        this.currentClickData = null;
        this.currentModalType = null; // Clear modal type when closing
        this.currentTradeData = null; // Clear stored trade data when closing
        console.log('🔒 Modal closed and data cleared');
    }

    getCurrentSymbol() {
        // Get current symbol from the chart or global state
        return window.currentSymbol || 'BTCUSDT';
    }

    getCurrentTimeframe() {
        // Get current timeframe from the chart or global state
        return window.currentTimeframe || '15m';
    }

    getCandlestickAtTime(targetTime) {
        console.log('🔍 getCandlestickAtTime called with targetTime:', targetTime);

        // Find the exact candlestick for the given timestamp
        // Try multiple ways to access the chart data
        let chartData = null;

        // Debug current data state
        console.log('🔍 Chart currentData type:', typeof this.chart.currentData);
        console.log('🔍 Chart currentData length:', this.chart.currentData?.length);
        console.log('🔍 Chart currentData sample:', this.chart.currentData?.[0]);

        if (this.chart.currentData && this.chart.currentData.length > 0) {
            chartData = this.chart.currentData;
            console.log('✅ Using chart.currentData with', chartData.length, 'candles');
            console.log('📊 Sample chart.currentData:', chartData.slice(-3));
            console.log('📊 Volume in sample data:', chartData.slice(-3).map(c => ({ time: c.time, volume: c.volume, hasVolume: c.volume > 0 })));

            // Check if ANY candle has volume > 0
            const withVolume = chartData.filter(c => c.volume && c.volume > 0);
            console.log(`📊 Candles with volume > 0: ${withVolume.length}/${chartData.length}`);
            if (withVolume.length === 0) {
                console.error('❌ CRITICAL: No candles in chart.currentData have volume > 0!');
                console.log('📊 All volume values:', chartData.map(c => c.volume).slice(0, 10));
            }
        } else if (this.chart.dataFeed && this.chart.dataFeed.data) {
            chartData = this.chart.dataFeed.data;
            console.log('✅ Using chart.dataFeed.data with', chartData.length, 'candles');
        } else if (this.chart.dataFeed && typeof this.chart.dataFeed.getData === 'function') {
            chartData = this.chart.dataFeed.getData();
            console.log('✅ Using chart.dataFeed.getData() with', chartData?.length, 'candles');
        } else {
            // Try to get data from TradingView series directly
            console.log('🔍 Trying to get data from series...');
            chartData = this.tryGetDataFromSeries();

            // If still no data, try chart API
            if (!chartData || chartData.length === 0) {
                console.log('🔍 Trying chart API exploration...');
                this.tryGetDataFromChartAPI(); // This is for exploration, doesn't return data yet
            }
        }

        if (!chartData || chartData.length === 0) {
            console.warn('❌ No chart data available in getCandlestickAtTime');
            console.log('🔍 Chart object keys:', Object.keys(this.chart));
            console.log('🔍 currentData details:', {
                exists: !!this.chart.currentData,
                type: typeof this.chart.currentData,
                length: this.chart.currentData?.length,
                isArray: Array.isArray(this.chart.currentData)
            });
            return null;
        }

        console.log(`✅ Found ${chartData.length} candles in chart data`);

        // Find the candlestick that matches the target time
        // TradingView time is usually in seconds, so we need to find the exact match
        let bestMatch = null;
        let minDifference = Infinity;

        for (const candle of chartData) {
            const timeDiff = Math.abs(candle.time - targetTime);
            if (timeDiff < minDifference) {
                minDifference = timeDiff;
                bestMatch = candle;
            }

            // If we find an exact match, use it
            if (timeDiff === 0) {
                break;
            }
        }

        if (bestMatch && minDifference < 3600) { // Within 1 hour tolerance
            console.log(`✅ Found candlestick match with ${minDifference}s difference:`, bestMatch);
            console.log(`📊 CRITICAL: bestMatch.volume = ${bestMatch.volume} (type: ${typeof bestMatch.volume})`);

            const result = {
                time: bestMatch.time,
                open: bestMatch.open,
                high: bestMatch.high,
                low: bestMatch.low,
                close: bestMatch.close,
                volume: bestMatch.volume || 0
            };

            console.log(`📊 CRITICAL: Returning OHLCV with volume = ${result.volume}`);
            if (result.volume === 0 && bestMatch.volume !== 0) {
                console.error('❌ CRITICAL: Volume was lost during OHLCV creation!');
                console.log('📊 Original bestMatch:', bestMatch);
                console.log('📊 Result object:', result);
            }

            return result;
        }

        // If no good match found, use the latest candle
        const latestCandle = chartData[chartData.length - 1];
        if (latestCandle) {
            console.log('Using latest candle as fallback:', latestCandle);
            return {
                time: latestCandle.time,
                open: latestCandle.open,
                high: latestCandle.high,
                low: latestCandle.low,
                close: latestCandle.close,
                volume: latestCandle.volume || 0
            };
        }

        return null;
    }

    // Deep search using TradingView Lightweight Charts official API methods
    tryGetDataFromSeries() {
        console.log('=== Deep TradingView Data Search ===');
        console.log('Candlestick series object:', this.chart.candlestickSeries);

        if (!this.chart.candlestickSeries) {
            console.log('No candlestick series available');
            return null;
        }

        try {
            // Method 1: Try to access data through TradingView's internal data model
            // Based on TradingView source code, series data is stored in internal models
            console.log('\n--- Method 1: Internal Data Model ---');
            const series = this.chart.candlestickSeries;

            // TradingView stores data in _internal._model._data or similar structures
            if (series._internal) {
                console.log('Found _internal:', series._internal);

                if (series._internal._model) {
                    console.log('Found _internal._model:', series._internal._model);

                    // Look for data in the model
                    const model = series._internal._model;
                    const modelProps = ['_data', 'data', '_bars', 'bars', '_items', 'items'];

                    for (const prop of modelProps) {
                        if (model[prop] && Array.isArray(model[prop]) && model[prop].length > 0) {
                            console.log(`Found data in _internal._model.${prop}:`, model[prop].length, 'items');
                            console.log('Sample data:', model[prop].slice(0, 2));
                            return this.convertTradingViewData(model[prop]);
                        }
                    }
                }

                // Try other internal properties
                const internalProps = ['_dataSource', '_series', '_seriesModel', '_bars', '_data'];
                for (const prop of internalProps) {
                    if (series._internal[prop]) {
                        console.log(`Found _internal.${prop}:`, typeof series._internal[prop]);

                        if (Array.isArray(series._internal[prop]) && series._internal[prop].length > 0) {
                            console.log(`Data found in _internal.${prop}:`, series._internal[prop].length, 'items');
                            return this.convertTradingViewData(series._internal[prop]);
                        }

                        // If it's an object, look deeper
                        if (typeof series._internal[prop] === 'object' && series._internal[prop] !== null) {
                            const deepData = this.searchObjectForData(series._internal[prop], `_internal.${prop}`);
                            if (deepData) return deepData;
                        }
                    }
                }
            }

            // Method 2: Try to get data through chart API using visible range
            console.log('\n--- Method 2: Chart API with Visible Range ---');
            if (this.chart.chart) {
                const timeScale = this.chart.chart.timeScale();
                const visibleRange = timeScale.getVisibleRange();
                console.log('Visible range:', visibleRange);

                if (visibleRange && visibleRange.from && visibleRange.to) {
                    // Try to extract data for the visible range
                    const data = this.extractDataFromVisibleRange(visibleRange);
                    if (data) return data;
                }
            }

            // Method 3: Deep recursive search through all properties
            console.log('\n--- Method 3: Deep Recursive Search ---');
            const foundData = this.deepSearchForCandlestickData(series, 'series', 0, 5);
            if (foundData) return foundData;

            // Method 4: Try to intercept data from chart updates
            console.log('\n--- Method 4: Chart Update Interception ---');
            const interceptedData = this.tryInterceptChartData();
            if (interceptedData) return interceptedData;

        } catch (error) {
            console.log('Error in deep data search:', error);
        }

        console.log('❌ No candlestick data found through any method');
        return null;
    }

    // Convert TradingView internal data format to standard OHLCV format
    convertTradingViewData(rawData) {
        console.log('Converting TradingView data format...');

        if (!Array.isArray(rawData) || rawData.length === 0) {
            return null;
        }

        // TradingView data might be in different formats
        const sample = rawData[0];
        console.log('Sample raw data item:', sample);

        // Try to convert based on the structure
        const convertedData = rawData.map(item => {
            // Handle different possible formats
            if (item.time !== undefined && item.open !== undefined) {
                // Already in correct format
                return item;
            } else if (item.t !== undefined && item.o !== undefined) {
                // Abbreviated format
                return {
                    time: item.t,
                    open: item.o,
                    high: item.h,
                    low: item.l,
                    close: item.c,
                    volume: item.v || 0
                };
            } else if (Array.isArray(item) && item.length >= 5) {
                // Array format [time, open, high, low, close, volume?]
                return {
                    time: item[0],
                    open: item[1],
                    high: item[2],
                    low: item[3],
                    close: item[4],
                    volume: item[5] || 0
                };
            }

            return null;
        }).filter(item => item !== null);

        console.log(`Converted ${convertedData.length} data items`);
        return convertedData.length > 0 ? convertedData : null;
    }

    // Deep recursive search for candlestick data
    deepSearchForCandlestickData(obj, path, depth, maxDepth) {
        if (depth >= maxDepth || !obj || typeof obj !== 'object') {
            return null;
        }

        // Check if this object looks like candlestick data
        if (Array.isArray(obj) && obj.length > 0) {
            const sample = obj[0];
            if (this.looksLikeCandlestickData(sample)) {
                console.log(`Found potential candlestick data at ${path}:`, obj.length, 'items');
                console.log('Sample:', sample);
                return this.convertTradingViewData(obj);
            }
        }

        // Recursively search object properties
        try {
            const keys = Object.keys(obj);
            for (const key of keys) {
                if (obj[key] && typeof obj[key] === 'object') {
                    const result = this.deepSearchForCandlestickData(obj[key], `${path}.${key}`, depth + 1, maxDepth);
                    if (result) return result;
                }
            }
        } catch (error) {
            // Ignore errors accessing properties
        }

        return null;
    }

    // Check if an object looks like candlestick data
    looksLikeCandlestickData(item) {
        if (!item || typeof item !== 'object') return false;

        // Check for standard OHLCV properties
        const hasStandardProps = item.time !== undefined &&
                                 item.open !== undefined &&
                                 item.high !== undefined &&
                                 item.low !== undefined &&
                                 item.close !== undefined;

        // Check for abbreviated properties
        const hasAbbrevProps = item.t !== undefined &&
                              item.o !== undefined &&
                              item.h !== undefined &&
                              item.l !== undefined &&
                              item.c !== undefined;

        // Check for array format
        const isArrayFormat = Array.isArray(item) && item.length >= 5;

        return hasStandardProps || hasAbbrevProps || isArrayFormat;
    }

    // Search object for data arrays
    searchObjectForData(obj, path) {
        if (!obj || typeof obj !== 'object') return null;

        const dataProps = ['data', '_data', 'items', '_items', 'values', '_values', 'bars', '_bars', 'points', '_points'];

        for (const prop of dataProps) {
            if (obj[prop] && Array.isArray(obj[prop]) && obj[prop].length > 0) {
                console.log(`Found data array at ${path}.${prop}:`, obj[prop].length, 'items');
                const converted = this.convertTradingViewData(obj[prop]);
                if (converted) return converted;
            }
        }

        return null;
    }

    // Try to intercept data from chart updates
    tryInterceptChartData() {
        console.log('Attempting to intercept chart data...');

        // This is a more advanced technique - try to access the chart's data through update mechanisms
        if (this.chart.dataFeed && this.chart.dataFeed.data && this.chart.dataFeed.data.length > 0) {
            console.log('Found data in dataFeed:', this.chart.dataFeed.data.length, 'items');
            return this.chart.dataFeed.data;
        }

        return null;
    }

    // Extract data from visible range using TradingView API
    extractDataFromVisibleRange(visibleRange) {
        console.log('Extracting data from visible range...');

        try {
            const timeScale = this.chart.chart.timeScale();
            const priceScale = this.chart.chart.priceScale('right');

            if (!timeScale || !priceScale) {
                console.log('No time or price scale available');
                return null;
            }

            // Try to sample data points across the visible range
            const chartContainer = this.chart.container;
            if (!chartContainer) return null;

            const rect = chartContainer.getBoundingClientRect();
            const width = rect.width;
            const samplePoints = Math.min(100, width / 10); // Sample every 10 pixels

            const extractedData = [];

            for (let i = 0; i < samplePoints; i++) {
                const x = (i / samplePoints) * width;

                try {
                    const time = timeScale.coordinateToTime(x);
                    if (time) {
                        // Try to get OHLCV data for this time point
                        const dataPoint = this.getDataPointAtTime(time);
                        if (dataPoint) {
                            extractedData.push(dataPoint);
                        }
                    }
                } catch (error) {
                    // Continue sampling other points
                }
            }

            if (extractedData.length > 0) {
                console.log(`Extracted ${extractedData.length} data points from visible range`);
                return extractedData;
            }

        } catch (error) {
            console.log('Error extracting data from visible range:', error);
        }

        return null;
    }

    // Try to get a data point at a specific time
    getDataPointAtTime(time) {
        // This is a placeholder - in a real implementation, we'd need to
        // access TradingView's internal data structures or use their API
        // to get the actual OHLCV data for a specific timestamp

        // For now, we'll return null and rely on other methods
        return null;
    }

    async getOHLCVData(timestamp) {
        console.log('🔍 Getting OHLCV data for timestamp:', timestamp);
        console.log('🔍 Current click data available:', !!this.currentClickData);
        console.log('🔍 Current click candlestick data:', this.currentClickData?.candlestickData);

        // First priority: Use candlestick data from the current click if available
        if (this.currentClickData && this.currentClickData.candlestickData) {
            console.log('✅ Using candlestick data from current click:', this.currentClickData.candlestickData);
            console.log('📊 Volume in candlestick data:', this.currentClickData.candlestickData.volume);
            return this.currentClickData.candlestickData;
        }

        // Fallback: check for old property name (candlestick) for backward compatibility
        if (this.currentClickData && this.currentClickData.candlestick) {
            console.log('✅ Using candlestick data from current click (legacy property):', this.currentClickData.candlestick);
            return this.currentClickData.candlestick;
        }

        // Second priority: Try to find the candlestick using the timestamp
        console.log('🔍 Attempting to get candlestick by timestamp...');
        const candlestickData = this.getCandlestickAtTime(timestamp);
        if (candlestickData) {
            console.log('✅ Found candlestick data by timestamp:', candlestickData);
            return candlestickData;
        }

        // Third priority: Try to get data from series
        console.log('🔍 Attempting to get data from series...');
        const seriesData = this.tryGetDataFromSeries();
        console.log('🔍 Series data result:', seriesData ? `${seriesData.length} items` : 'null');

        if (seriesData && seriesData.length > 0) {
            // Find the closest data point to the timestamp
            const targetTime = Math.floor(timestamp);
            let closestData = null;
            let minTimeDiff = Infinity;

            console.log('🔍 Searching for closest data point to timestamp:', targetTime);
            for (const dataPoint of seriesData) {
                const dataTime = Math.floor(dataPoint.time || dataPoint.t || 0);
                const timeDiff = Math.abs(dataTime - targetTime);

                if (timeDiff < minTimeDiff) {
                    minTimeDiff = timeDiff;
                    closestData = dataPoint;
                }
            }

            if (closestData) {
                console.log('✅ Found closest OHLCV data from series (diff:', minTimeDiff, 's):', closestData);
                return this.normalizeOHLCVData(closestData);
            }
        }

        // Fourth priority: Try to get OHLCV data from backend API
        console.log('⚠️  No frontend OHLCV data available, attempting to fetch from backend...');
        try {
            const backendOHLCV = await this.fetchOHLCVFromBackend(timestamp);
            if (backendOHLCV) {
                console.log('✅ Using backend OHLCV data:', backendOHLCV);
                return backendOHLCV;
            }
        } catch (error) {
            console.warn('❌ Failed to fetch OHLCV from backend:', error);
        }

        // Don't create synthetic data - return null if we can't get real candlestick data
        console.warn('❌ No candlestick data available from any source');
        console.log('🔍 Debug info:');
        console.log('   - Chart object:', !!this.chart);
        console.log('   - Chart currentData:', !!this.chart?.currentData);
        console.log('   - Chart dataFeed:', !!this.chart?.dataFeed);
        console.log('   - Chart dataFeed.data:', !!this.chart?.dataFeed?.data);
        return null;
    }

    // Normalize OHLCV data to ensure consistent format
    normalizeOHLCVData(data) {
        if (!data) return null;

        console.log('🔧 Normalizing OHLCV data:', data);
        console.log('📊 Original volume value:', data.volume);

        // Handle different data formats
        let normalized = {};

        if (data.time !== undefined && data.open !== undefined) {
            // Standard format
            normalized = {
                time: data.time,
                open: parseFloat(data.open),
                high: parseFloat(data.high),
                low: parseFloat(data.low),
                close: parseFloat(data.close),
                volume: parseFloat(data.volume || 0)
            };
            console.log('📊 Normalized volume (standard format):', normalized.volume);
        } else if (data.t !== undefined && data.o !== undefined) {
            // Abbreviated format
            normalized = {
                time: data.t,
                open: parseFloat(data.o),
                high: parseFloat(data.h),
                low: parseFloat(data.l),
                close: parseFloat(data.c),
                volume: parseFloat(data.v || 0)
            };
        } else if (Array.isArray(data) && data.length >= 5) {
            // Array format [time, open, high, low, close, volume?]
            normalized = {
                time: data[0],
                open: parseFloat(data[1]),
                high: parseFloat(data[2]),
                low: parseFloat(data[3]),
                close: parseFloat(data[4]),
                volume: parseFloat(data[5] || 0)
            };
        } else {
            console.warn('Unknown OHLCV data format:', data);
            return null;
        }

        // Validate the normalized data
        if (isNaN(normalized.open) || isNaN(normalized.high) ||
            isNaN(normalized.low) || isNaN(normalized.close)) {
            console.warn('Invalid OHLCV data values:', normalized);
            return null;
        }

        console.log('✅ Normalized OHLCV data:', normalized);
        return normalized;
    }

    // Helper method to safely parse OHLCV data from JSON strings
    parseOHLCVData(data) {
        if (!data) return null;

        try {
            if (typeof data === 'string') {
                return JSON.parse(data);
            }
            return data;
        } catch (error) {
            console.warn('Error parsing OHLCV data:', error);
            return null;
        }
    }

    // Helper method to safely parse indicator data from JSON strings
    parseIndicatorData(data) {
        if (!data) return null;

        try {
            if (typeof data === 'string') {
                return JSON.parse(data);
            }
            return data;
        } catch (error) {
            console.warn('Error parsing indicator data:', error);
            return null;
        }
    }

    // Try to access data through TradingView chart API
    tryGetDataFromChartAPI() {
        console.log('Attempting to get data from TradingView chart API...');

        if (!this.chart.chart) {
            console.log('No chart API available');
            return null;
        }

        try {
            // Try to get visible range and data
            const timeScale = this.chart.chart.timeScale();
            const visibleRange = timeScale.getVisibleRange();
            console.log('Visible range:', visibleRange);

            // Try to get series data through different methods
            if (this.chart.candlestickSeries) {
                console.log('Trying to access series data through chart API...');

                // Try to get data at specific coordinates
                const chartContainer = this.chart.container;
                if (chartContainer) {
                    const rect = chartContainer.getBoundingClientRect();
                    const centerX = rect.width / 2;

                    try {
                        const time = timeScale.coordinateToTime(centerX);
                        console.log('Center time:', time);

                        // This might give us access to the data at that time
                        const priceScale = this.chart.chart.priceScale('right');
                        console.log('Price scale:', priceScale);

                    } catch (error) {
                        console.log('Error getting time/price from coordinates:', error);
                    }
                }
            }

        } catch (error) {
            console.log('Error accessing chart API:', error);
        }

        return null;
    }

    async getIndicatorData(timestamp) {
        // Get indicator data if available
        const indicators = {};
        console.log('🔍 getIndicatorData called for timestamp:', timestamp);

        try {
            // First try to get data from indicatorsManager.indicatorData (calculated values)
            if (window.indicatorsManager && window.indicatorsManager.indicatorData) {
                const indicatorData = window.indicatorsManager.indicatorData;
                console.log('📊 Available indicator data:', Object.keys(indicatorData));

                if (Object.keys(indicatorData).length > 0) {
                    // Extract the latest values from the indicator arrays
                    const latestIndicators = {};

                    // Get EMA values (latest from arrays)
                    if (indicatorData.ema) {
                        const emaData = {};
                        Object.keys(indicatorData.ema).forEach(key => {
                            const values = indicatorData.ema[key];
                            if (values && values.length > 0) {
                                emaData[key] = values[values.length - 1]; // Get latest value
                            }
                        });
                        if (Object.keys(emaData).length > 0) {
                            latestIndicators.ema = emaData;
                        }
                    }

                    // Get RSI values (latest from arrays)
                    if (indicatorData.rsi) {
                        const rsiData = {};
                        Object.keys(indicatorData.rsi).forEach(key => {
                            const values = indicatorData.rsi[key];
                            if (values && values.length > 0) {
                                rsiData[key] = values[values.length - 1]; // Get latest value
                            }
                        });
                        if (Object.keys(rsiData).length > 0) {
                            latestIndicators.rsi = rsiData;
                        }
                    }

                    // Get MACD values (latest from arrays)
                    if (indicatorData.macd) {
                        const macdData = {};
                        if (indicatorData.macd.macd && indicatorData.macd.macd.length > 0) {
                            macdData.macd = indicatorData.macd.macd[indicatorData.macd.macd.length - 1];
                        }
                        if (indicatorData.macd.signal && indicatorData.macd.signal.length > 0) {
                            macdData.signal = indicatorData.macd.signal[indicatorData.macd.signal.length - 1];
                        }
                        if (indicatorData.macd.histogram && indicatorData.macd.histogram.length > 0) {
                            macdData.histogram = indicatorData.macd.histogram[indicatorData.macd.histogram.length - 1];
                        }
                        if (Object.keys(macdData).length > 0) {
                            latestIndicators.macd = macdData;
                        }
                    }

                    // Get Bollinger Bands values (latest from arrays)
                    if (indicatorData.bollinger_bands) {
                        const bbData = {};
                        if (indicatorData.bollinger_bands.upper && indicatorData.bollinger_bands.upper.length > 0) {
                            bbData.upper = indicatorData.bollinger_bands.upper[indicatorData.bollinger_bands.upper.length - 1];
                        }
                        if (indicatorData.bollinger_bands.middle && indicatorData.bollinger_bands.middle.length > 0) {
                            bbData.middle = indicatorData.bollinger_bands.middle[indicatorData.bollinger_bands.middle.length - 1];
                        }
                        if (indicatorData.bollinger_bands.lower && indicatorData.bollinger_bands.lower.length > 0) {
                            bbData.lower = indicatorData.bollinger_bands.lower[indicatorData.bollinger_bands.lower.length - 1];
                        }
                        if (Object.keys(bbData).length > 0) {
                            latestIndicators.bollinger_bands = bbData;
                        }
                    }

                    if (Object.keys(latestIndicators).length > 0) {
                        console.log('✅ Using latest calculated indicator data:', latestIndicators);
                        return latestIndicators;
                    }
                }
            }

            // Fallback: try to get data from indicator series if available
            if (window.indicatorsManager && window.indicatorsManager.indicatorSeries) {
                const series = window.indicatorsManager.indicatorSeries;
                console.log('📊 Available indicator series:', Object.keys(series));

                // EMA indicators
                const emaData = {};
                Object.keys(series).forEach(key => {
                    if (key.includes('ema') || key.includes('EMA')) {
                        const seriesData = series[key];
                        if (seriesData && seriesData.data) {
                            const latestValue = seriesData.data[seriesData.data.length - 1];
                            if (latestValue) {
                                emaData[key] = latestValue.value || latestValue;
                            }
                        }
                    }
                });
                if (Object.keys(emaData).length > 0) {
                    indicators.ema = emaData;
                }

                // RSI indicators
                const rsiData = {};
                Object.keys(series).forEach(key => {
                    if (key.includes('rsi') || key.includes('RSI')) {
                        const seriesData = series[key];
                        if (seriesData && seriesData.data) {
                            const latestValue = seriesData.data[seriesData.data.length - 1];
                            if (latestValue) {
                                rsiData[key] = latestValue.value || latestValue;
                            }
                        }
                    }
                });
                if (Object.keys(rsiData).length > 0) {
                    indicators.rsi = rsiData;
                }

                // MACD indicators
                const macdData = {};
                Object.keys(series).forEach(key => {
                    if (key.includes('macd') || key.includes('MACD')) {
                        const seriesData = series[key];
                        if (seriesData && seriesData.data) {
                            const latestValue = seriesData.data[seriesData.data.length - 1];
                            if (latestValue) {
                                macdData[key] = latestValue.value || latestValue;
                            }
                        }
                    }
                });
                if (Object.keys(macdData).length > 0) {
                    indicators.macd = macdData;
                }

                // Bollinger Bands
                const bbData = {};
                Object.keys(series).forEach(key => {
                    if (key.includes('bollinger') || key.includes('bb') || key.includes('BB')) {
                        const seriesData = series[key];
                        if (seriesData && seriesData.data) {
                            const latestValue = seriesData.data[seriesData.data.length - 1];
                            if (latestValue) {
                                bbData[key] = latestValue.value || latestValue;
                            }
                        }
                    }
                });
                if (Object.keys(bbData).length > 0) {
                    indicators.bollinger_bands = bbData;
                }
            }

            // Try to get indicator data from the backend API if no frontend indicators available
            if (Object.keys(indicators).length === 0) {
                console.log('⚠️  No frontend indicators available, attempting to fetch from backend...');
                console.log('🔍 Indicators manager state:', {
                    exists: !!window.indicatorsManager,
                    hasSeries: !!window.indicatorsManager?.indicatorSeries,
                    seriesCount: window.indicatorsManager?.indicatorSeries ? Object.keys(window.indicatorsManager.indicatorSeries).length : 0
                });

                try {
                    const backendIndicators = await this.fetchIndicatorsFromBackend(timestamp);
                    if (backendIndicators && Object.keys(backendIndicators).length > 0) {
                        Object.assign(indicators, backendIndicators);
                        console.log('✅ Using backend indicator data:', indicators);
                    } else {
                        console.log('❌ No indicator data available from backend either');
                        // Check if we should provide sample data for testing
                        console.log('💡 Consider adding indicators to the chart or enabling backend indicator API');
                        return null;
                    }
                } catch (error) {
                    console.warn('Failed to fetch indicators from backend:', error);
                    return null;
                }
            } else {
                console.log('✅ Using real frontend indicator data:', indicators);
            }

        } catch (error) {
            console.warn('Error getting indicator data:', error);
        }

        return indicators;
    }

    async fetchIndicatorsFromBackend(timestamp) {
        /**
         * Fetch indicator data from backend API for the given timestamp
         */
        try {
            const symbol = this.getCurrentSymbol();
            const timeframe = this.getCurrentTimeframe();

            // Convert timestamp to ISO format for API
            const isoTimestamp = new Date(timestamp * 1000).toISOString();

            const response = await fetch(`/api/v1/indicators/data?symbol=${symbol}&timeframe=${timeframe}&timestamp=${isoTimestamp}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.data) {
                    console.log('✅ Fetched indicators from backend:', data.data);
                    return data.data;
                }
            } else {
                console.warn('Backend indicators API returned:', response.status);
            }
        } catch (error) {
            console.warn('Error fetching indicators from backend:', error);
        }

        return null;
    }

    async fetchOHLCVFromBackend(timestamp) {
        /**
         * Fetch OHLCV data from backend API for the given timestamp
         */
        try {
            const symbol = this.getCurrentSymbol();
            const timeframe = this.getCurrentTimeframe();

            // Convert timestamp to ISO format for API
            const targetDate = new Date(timestamp * 1000);
            const startTime = new Date(targetDate.getTime() - 60 * 60 * 1000).toISOString(); // 1 hour before
            const endTime = new Date(targetDate.getTime() + 60 * 60 * 1000).toISOString(); // 1 hour after

            const response = await fetch(`/api/v1/ohlcv/data?symbol=${symbol}&timeframe=${timeframe}&start_time=${startTime}&end_time=${endTime}&limit=10`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.data && data.data.length > 0) {
                    // Find the closest OHLCV data point to our timestamp
                    let closestData = null;
                    let minTimeDiff = Infinity;

                    for (const ohlcv of data.data) {
                        const ohlcvTime = new Date(ohlcv.timestamp).getTime() / 1000;
                        const timeDiff = Math.abs(ohlcvTime - timestamp);

                        if (timeDiff < minTimeDiff) {
                            minTimeDiff = timeDiff;
                            closestData = {
                                time: ohlcvTime,
                                open: parseFloat(ohlcv.open),
                                high: parseFloat(ohlcv.high),
                                low: parseFloat(ohlcv.low),
                                close: parseFloat(ohlcv.close),
                                volume: parseFloat(ohlcv.volume)
                            };
                        }
                    }

                    if (closestData && minTimeDiff < 3600) { // Within 1 hour
                        console.log('✅ Fetched OHLCV from backend:', closestData);
                        return closestData;
                    }
                }
            } else {
                console.warn('Backend OHLCV API returned:', response.status);
            }
        } catch (error) {
            console.warn('Error fetching OHLCV from backend:', error);
        }

        return null;
    }

    setElementText(elementId, text) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = text;
        }
    }

    setElementTextWithHover(elementId, text, tooltipContent) {
        const element = document.getElementById(elementId);
        if (element) {
            element.innerHTML = `
                <span class="hover-details">
                    ${text}
                    <div class="tooltip">
                        <div class="tooltip-content">
                            ${tooltipContent}
                        </div>
                    </div>
                </span>
            `;
        }
    }

    createPriceTooltip(price) {
        // Use stored trade data if available, fallback to current click data
        const timestamp = this.currentTradeData?.timestamp || this.currentClickData?.time || 'Unknown';

        return `
            <div class="tooltip-row">
                <span class="tooltip-label">Exact Price:</span>
                <span class="tooltip-value">$${price.toFixed(8)}</span>
            </div>
            <div class="tooltip-row">
                <span class="tooltip-label">Rounded:</span>
                <span class="tooltip-value">$${price.toFixed(2)}</span>
            </div>
            <div class="tooltip-row">
                <span class="tooltip-label">Timestamp:</span>
                <span class="tooltip-value">${timestamp}</span>
            </div>
        `;
    }

    createOHLCVTooltip(ohlcvData) {
        if (!ohlcvData) return 'No OHLCV data available';

        const range = ohlcvData.high - ohlcvData.low;
        const bodySize = Math.abs(ohlcvData.close - ohlcvData.open);
        const upperWick = ohlcvData.high - Math.max(ohlcvData.open, ohlcvData.close);
        const lowerWick = Math.min(ohlcvData.open, ohlcvData.close) - ohlcvData.low;

        return `
            <div class="tooltip-row">
                <span class="tooltip-label">Range:</span>
                <span class="tooltip-value">$${range.toFixed(2)}</span>
            </div>
            <div class="tooltip-row">
                <span class="tooltip-label">Body Size:</span>
                <span class="tooltip-value">$${bodySize.toFixed(2)}</span>
            </div>
            <div class="tooltip-row">
                <span class="tooltip-label">Upper Wick:</span>
                <span class="tooltip-value">$${upperWick.toFixed(2)}</span>
            </div>
            <div class="tooltip-row">
                <span class="tooltip-label">Lower Wick:</span>
                <span class="tooltip-value">$${lowerWick.toFixed(2)}</span>
            </div>
            <div class="tooltip-row">
                <span class="tooltip-label">Volume:</span>
                <span class="tooltip-value">${ohlcvData.volume.toLocaleString()}</span>
            </div>
        `;
    }

    populateOHLCVData(containerId, ohlcvData) {
        const container = document.getElementById(containerId);
        if (!container) return;

        if (!ohlcvData) {
            container.innerHTML = '<div class="data-row"><span class="data-label">No OHLCV data available</span></div>';
            return;
        }

        const tooltipContent = this.createOHLCVTooltip(ohlcvData);

        container.innerHTML = `
            <div class="data-row">
                <span class="data-label">Open:</span>
                <span class="data-value hover-details">
                    $${ohlcvData.open.toFixed(2)}
                    <div class="tooltip">
                        <div class="tooltip-content">
                            <div class="tooltip-row">
                                <span class="tooltip-label">Exact:</span>
                                <span class="tooltip-value">$${ohlcvData.open.toFixed(8)}</span>
                            </div>
                        </div>
                    </div>
                </span>
            </div>
            <div class="data-row">
                <span class="data-label">High:</span>
                <span class="data-value hover-details">
                    $${ohlcvData.high.toFixed(2)}
                    <div class="tooltip">
                        <div class="tooltip-content">
                            <div class="tooltip-row">
                                <span class="tooltip-label">Exact:</span>
                                <span class="tooltip-value">$${ohlcvData.high.toFixed(8)}</span>
                            </div>
                        </div>
                    </div>
                </span>
            </div>
            <div class="data-row">
                <span class="data-label">Low:</span>
                <span class="data-value hover-details">
                    $${ohlcvData.low.toFixed(2)}
                    <div class="tooltip">
                        <div class="tooltip-content">
                            <div class="tooltip-row">
                                <span class="tooltip-label">Exact:</span>
                                <span class="tooltip-value">$${ohlcvData.low.toFixed(8)}</span>
                            </div>
                        </div>
                    </div>
                </span>
            </div>
            <div class="data-row">
                <span class="data-label">Close:</span>
                <span class="data-value hover-details">
                    $${ohlcvData.close.toFixed(2)}
                    <div class="tooltip">
                        <div class="tooltip-content">
                            <div class="tooltip-row">
                                <span class="tooltip-label">Exact:</span>
                                <span class="tooltip-value">$${ohlcvData.close.toFixed(8)}</span>
                            </div>
                        </div>
                    </div>
                </span>
            </div>
            <div class="data-row">
                <span class="data-label">Volume:</span>
                <span class="data-value hover-details">
                    ${ohlcvData.volume.toLocaleString()}
                    <div class="tooltip">
                        <div class="tooltip-content">
                            ${tooltipContent}
                        </div>
                    </div>
                </span>
            </div>
        `;
    }

    populateIndicatorData(containerId, indicatorData) {
        const container = document.getElementById(containerId);
        if (!container) return;

        if (!indicatorData || Object.keys(indicatorData).length === 0) {
            container.innerHTML = '<div class="indicator-group"><span class="data-label">No indicator data available</span></div>';
            return;
        }

        let html = '';

        // EMA indicators
        if (indicatorData.ema) {
            html += '<div class="indicator-group">';
            html += '<h5>Exponential Moving Averages</h5>';
            html += '<div class="indicator-values">';
            Object.entries(indicatorData.ema).forEach(([key, value]) => {
                const label = key.replace('_', ' ').toUpperCase();
                html += `
                    <div class="indicator-item">
                        <span class="indicator-label">${label}:</span>
                        <span class="indicator-value">$${value.toFixed(2)}</span>
                    </div>
                `;
            });
            html += '</div></div>';
        }

        // RSI indicators
        if (indicatorData.rsi) {
            html += '<div class="indicator-group">';
            html += '<h5>Relative Strength Index</h5>';
            html += '<div class="indicator-values">';
            Object.entries(indicatorData.rsi).forEach(([key, value]) => {
                const label = key.replace('_', ' ').toUpperCase();
                const colorClass = value > 70 ? 'pnl-negative' : value < 30 ? 'pnl-positive' : '';
                html += `
                    <div class="indicator-item">
                        <span class="indicator-label">${label}:</span>
                        <span class="indicator-value ${colorClass}">${value.toFixed(1)}</span>
                    </div>
                `;
            });
            html += '</div></div>';
        }

        // MACD indicators
        if (indicatorData.macd) {
            html += '<div class="indicator-group">';
            html += '<h5>MACD</h5>';
            html += '<div class="indicator-values">';

            // Display MACD line
            if (indicatorData.macd.macd !== undefined) {
                const macdValue = indicatorData.macd.macd;
                const colorClass = macdValue > 0 ? 'pnl-positive' : macdValue < 0 ? 'pnl-negative' : 'pnl-neutral';
                html += `
                    <div class="indicator-item">
                        <span class="indicator-label">MACD:</span>
                        <span class="indicator-value ${colorClass}">${macdValue.toFixed(3)}</span>
                    </div>
                `;
            }

            // Display Signal line
            if (indicatorData.macd.signal !== undefined) {
                const signalValue = indicatorData.macd.signal;
                const colorClass = signalValue > 0 ? 'pnl-positive' : signalValue < 0 ? 'pnl-negative' : 'pnl-neutral';
                html += `
                    <div class="indicator-item">
                        <span class="indicator-label">Signal:</span>
                        <span class="indicator-value ${colorClass}">${signalValue.toFixed(3)}</span>
                    </div>
                `;
            }

            // Display Histogram
            if (indicatorData.macd.histogram !== undefined) {
                const histogramValue = indicatorData.macd.histogram;
                const colorClass = histogramValue > 0 ? 'pnl-positive' : histogramValue < 0 ? 'pnl-negative' : 'pnl-neutral';
                html += `
                    <div class="indicator-item">
                        <span class="indicator-label">Histogram:</span>
                        <span class="indicator-value ${colorClass}">${histogramValue.toFixed(3)}</span>
                    </div>
                `;
            }

            html += '</div></div>';
        }

        // Bollinger Bands
        if (indicatorData.bollinger_bands) {
            html += '<div class="indicator-group">';
            html += '<h5>Bollinger Bands</h5>';
            html += '<div class="indicator-values">';
            Object.entries(indicatorData.bollinger_bands).forEach(([key, value]) => {
                const label = key.replace('_', ' ').toUpperCase();
                html += `
                    <div class="indicator-item">
                        <span class="indicator-label">${label}:</span>
                        <span class="indicator-value">$${value.toFixed(2)}</span>
                    </div>
                `;
            });
            html += '</div></div>';
        }

        container.innerHTML = html;
    }

    updatePnLCalculation() {
        const entrySelect = document.getElementById('exit-entry-select');
        const quantityInput = document.getElementById('exit-quantity');
        const pnlSection = document.getElementById('exit-pnl-section');
        const pnlContainer = document.getElementById('exit-pnl');

        if (!entrySelect.value || !pnlSection || !pnlContainer) {
            if (pnlSection) pnlSection.style.display = 'none';
            return;
        }

        const selectedEntry = this.marks.get(entrySelect.value);
        if (!selectedEntry) {
            pnlSection.style.display = 'none';
            return;
        }

        // Handle both old and new schema formats
        const entryPrice = selectedEntry.entry_price || selectedEntry.price || 0;
        const side = selectedEntry.side || (selectedEntry.entry_side ? selectedEntry.entry_side.toLowerCase() : null) || 'buy';
        const entryQuantity = selectedEntry.quantity || 1; // Default quantity for new schema

        const exitPrice = this.currentClickData.price;
        const quantity = parseFloat(quantityInput.value) || entryQuantity;

        // Calculate P&L
        let priceDiff = exitPrice - entryPrice;
        if (side === 'sell') {
            priceDiff = -priceDiff; // Reverse for short positions
        }

        const absolutePnL = priceDiff * quantity;
        const percentagePnL = (priceDiff / entryPrice) * 100;

        // Determine color class
        const colorClass = absolutePnL > 0 ? 'pnl-positive' : absolutePnL < 0 ? 'pnl-negative' : 'pnl-neutral';

        // Populate P&L data
        pnlContainer.innerHTML = `
            <div class="data-row">
                <span class="data-label">Entry Price:</span>
                <span class="data-value">$${entryPrice.toFixed(2)}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Exit Price:</span>
                <span class="data-value">$${exitPrice.toFixed(2)}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Price Difference:</span>
                <span class="data-value ${colorClass}">$${priceDiff.toFixed(2)}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Quantity:</span>
                <span class="data-value">${quantity}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Absolute P&L:</span>
                <span class="data-value ${colorClass}">$${absolutePnL.toFixed(2)}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Percentage P&L:</span>
                <span class="data-value ${colorClass}">${percentagePnL.toFixed(2)}%</span>
            </div>
        `;

        pnlSection.style.display = 'block';

        // Update quantity input if empty
        if (!quantityInput.value) {
            quantityInput.value = entryQuantity;
        }

        // Add event listener to quantity input to update P&L in real-time
        quantityInput.removeEventListener('input', this.updatePnLCalculation.bind(this));
        quantityInput.addEventListener('input', this.updatePnLCalculation.bind(this));
    }

    addMarkToChart(markData) {
        // Store mark data
        this.marks.set(markData.id, markData);

        // Determine if this is an entry or exit mark
        const markType = markData.mark_type || 'ENTRY';

        if (markType === 'EXIT') {
            // For exit marks, just refresh all markers to show the updated entry with exit
            console.log('📊 Adding exit mark - refreshing all chart markers');
            this.refreshChartMarkers();
            return;
        }

        // Handle entry marks
        let side = markData.side || (markData.entry_side ? markData.entry_side.toLowerCase() : null) || 'buy';

        // Ensure side is a valid string
        if (!side || typeof side !== 'string') {
            side = 'buy'; // Default fallback
        }

        const entryPrice = markData.entry_price || markData.price || 0;
        const rawTimestamp = markData.entry_timestamp || markData.timestamp;

        // Convert timestamp to Unix timestamp for TradingView with proper alignment
        let entryTimestamp;
        if (typeof rawTimestamp === 'string') {
            entryTimestamp = Math.floor(new Date(rawTimestamp).getTime() / 1000);
        } else if (typeof rawTimestamp === 'number') {
            entryTimestamp = rawTimestamp > 1000000000000 ?
                Math.floor(rawTimestamp / 1000) :
                Math.floor(rawTimestamp);
        } else {
            entryTimestamp = rawTimestamp;
        }

        // Ensure timestamp alignment with chart data for intraday intervals
        const originalTimestamp = entryTimestamp;
        entryTimestamp = this.alignTimestampWithChart(entryTimestamp);

        // Debug logging for timestamp alignment
        if (entryTimestamp !== originalTimestamp) {
            console.log(`🎯 Entry timestamp aligned: ${originalTimestamp} -> ${entryTimestamp}`);
            console.log(`   Original: ${new Date(originalTimestamp * 1000).toISOString()}`);
            console.log(`   Aligned:  ${new Date(entryTimestamp * 1000).toISOString()}`);
        }

        // Add visual marker to chart for entry
        const color = side === 'buy' ? '#4caf50' : '#f44336';

        // Get adaptive marker properties
        const timeScale = this.chart.chart.timeScale();
        const barSpacing = timeScale.options().barSpacing || 6;

        // Use same adaptive logic as bulk marker creation for consistency
        let size;
        let priceText;

        if (barSpacing < 1) {
            // Extremely zoomed out - use maximum size and minimal text
            size = 6; // Maximum size to force visibility
            priceText = entryPrice >= 1000 ? `${(entryPrice / 1000).toFixed(0)}K` : `${entryPrice.toFixed(0)}`;
        } else if (barSpacing < 2) {
            // Very zoomed out - large size with compact text
            size = 5;
            priceText = entryPrice >= 1000 ? `$${(entryPrice / 1000).toFixed(1)}K` : `$${entryPrice.toFixed(0)}`;
        } else if (barSpacing < 4) {
            // Zoomed out - medium size with compact text
            size = 4;
            priceText = entryPrice >= 1000 ? `$${(entryPrice / 1000).toFixed(1)}K` : `$${entryPrice.toFixed(0)}`;
        } else if (barSpacing < 8) {
            // Normal zoom - standard size with full text
            size = 4;
            priceText = `$${entryPrice.toFixed(2)}`;
        } else {
            // Zoomed in - large size with full text
            size = 5;
            priceText = `$${entryPrice.toFixed(2)}`;
        }

        // Single marker - force text visibility with multiple strategies
        const marker = {
            time: entryTimestamp,
            position: 'belowBar',
            color: color,
            shape: 'circle',
            size: size
        };

        // Strategy 1: Always include text
        marker.text = `${side.toUpperCase()} @ ${priceText}`;

        // Strategy 2: For very zoomed out views, use minimal but visible text
        if (barSpacing < 1) {
            marker.text = side === 'buy' ? 'B' : 'S'; // Single letter for extreme zoom
        } else if (barSpacing < 2) {
            marker.text = `${side.toUpperCase()}`; // Just BUY/SELL
        }

        // Add marker to candlestick series
        if (this.chart.candlestickSeries) {
            const existingMarkers = this.chart.candlestickSeries.markers() || [];
            this.chart.candlestickSeries.setMarkers([...existingMarkers, marker]);
        }

        console.log(`📊 Added ${markType} mark to chart:`, marker);
    }

    updateMarkOnChart(markData) {
        // Update stored mark data
        this.marks.set(markData.id, markData);

        // Update visual representation if needed
        this.refreshChartMarkers();
    }

    refreshChartMarkers() {
        if (!this.chart.candlestickSeries) return;

        const markers = [];

        // Get current time scale information for adaptive sizing
        const timeScale = this.chart.chart.timeScale();
        const visibleRange = timeScale.getVisibleLogicalRange();
        const barSpacing = timeScale.options().barSpacing || 6;

        // Calculate adaptive marker properties based on zoom level
        const getAdaptiveMarkerProps = (price) => {
            // TradingView hides text when zoomed out - we need to force visibility
            // Use maximum size and ensure text is always present
            let size;
            let priceText;

            if (barSpacing < 1) {
                // Extremely zoomed out - use maximum size and minimal text
                size = 6; // Maximum size to force visibility
                priceText = price >= 1000 ? `${(price / 1000).toFixed(0)}K` : `${price.toFixed(0)}`;
            } else if (barSpacing < 2) {
                // Very zoomed out - large size with compact text
                size = 5;
                priceText = price >= 1000 ? `$${(price / 1000).toFixed(1)}K` : `$${price.toFixed(0)}`;
            } else if (barSpacing < 4) {
                // Zoomed out - medium size with compact text
                size = 4;
                priceText = price >= 1000 ? `$${(price / 1000).toFixed(1)}K` : `$${price.toFixed(0)}`;
            } else if (barSpacing < 8) {
                // Normal zoom - standard size with full text
                size = 4;
                priceText = `$${price.toFixed(2)}`;
            } else {
                // Zoomed in - large size with full text
                size = 5;
                priceText = `$${price.toFixed(2)}`;
            }

            console.log(`📏 Marker props for price ${price}: size=${size}, text="${priceText}", barSpacing=${barSpacing}`);
            return { size, priceText };
        };

        this.marks.forEach(mark => {
            // Helper function to convert timestamp to Unix timestamp for TradingView
            const convertTimestamp = (timestamp) => {
                let unixTimestamp;
                if (typeof timestamp === 'string') {
                    unixTimestamp = Math.floor(new Date(timestamp).getTime() / 1000);
                } else if (typeof timestamp === 'number') {
                    unixTimestamp = timestamp > 1000000000000 ?
                        Math.floor(timestamp / 1000) :
                        Math.floor(timestamp);
                } else {
                    unixTimestamp = timestamp;
                }

                // Ensure timestamp alignment with chart data for intraday intervals
                const alignedTimestamp = this.alignTimestampWithChart(unixTimestamp);

                // Debug logging for timestamp alignment
                if (alignedTimestamp !== unixTimestamp) {
                    console.log(`🎯 Timestamp aligned: ${unixTimestamp} -> ${alignedTimestamp} (${new Date(alignedTimestamp * 1000).toISOString()})`);
                }

                return alignedTimestamp;
            };

            // Check if this is a standalone EXIT mark
            if (mark.mark_type === 'EXIT') {
                // This is a standalone exit mark - display as yellow square
                const exitPrice = mark.price || 0;
                const exitTimestamp = convertTimestamp(mark.timestamp);
                const { size, priceText } = getAdaptiveMarkerProps(exitPrice);

                // Standalone exit marker - force text visibility
                const exitMarker = {
                    time: exitTimestamp,
                    position: 'aboveBar',
                    color: '#ffeb3b', // Yellow color for exit marks
                    shape: 'square',
                    size: size
                };

                // Strategy 1: Always include text
                exitMarker.text = `EXIT @ ${priceText}`;

                // Strategy 2: For very zoomed out views, use minimal but visible text
                if (barSpacing < 1) {
                    exitMarker.text = 'X'; // Single letter for extreme zoom
                } else if (barSpacing < 2) {
                    exitMarker.text = 'EXIT'; // Just EXIT
                }

                markers.push(exitMarker);
                return; // Skip the entry marker logic for EXIT marks
            }

            // Handle ENTRY marks and legacy marks
            let side = mark.side || (mark.entry_side ? mark.entry_side.toLowerCase() : null) || 'buy';

            // Ensure side is a valid string
            if (!side || typeof side !== 'string') {
                side = 'buy'; // Default fallback
            }

            const entryPrice = mark.entry_price || mark.price || 0;
            const entryTimestamp = convertTimestamp(mark.entry_timestamp || mark.timestamp);
            const exitPrice = mark.exit_price || null;
            const exitTimestamp = mark.exit_timestamp ? convertTimestamp(mark.exit_timestamp) : null;

            const color = side === 'buy' ? '#4caf50' : '#f44336';
            const { size: entrySize, priceText: entryPriceText } = getAdaptiveMarkerProps(entryPrice);

            // Entry marker - force text visibility with multiple strategies
            const entryMarker = {
                time: entryTimestamp,
                position: 'belowBar',
                color: color,
                shape: 'circle',
                size: entrySize
            };

            // Strategy 1: Always include text, even if TradingView might hide it
            entryMarker.text = `${side.toUpperCase()} @ ${entryPriceText}`;

            // Strategy 2: For very zoomed out views, use minimal but visible text
            if (barSpacing < 1) {
                entryMarker.text = side === 'buy' ? 'B' : 'S'; // Single letter for extreme zoom
            } else if (barSpacing < 2) {
                entryMarker.text = `${side.toUpperCase()}`; // Just BUY/SELL
            }

            markers.push(entryMarker);

            // Exit marker if exists (for legacy combined entry/exit marks)
            // Only show exit marker from entry mark if there's no separate EXIT mark for this entry
            if (exitTimestamp && exitPrice) {
                // Check if there's a separate EXIT mark linked to this entry
                let hasSeparateExitMark = false;
                this.marks.forEach(otherMark => {
                    if (otherMark.mark_type === 'EXIT' && otherMark.linked_trade_id === mark.id) {
                        hasSeparateExitMark = true;
                    }
                });

                // Only show exit marker from entry if there's no separate EXIT mark
                if (!hasSeparateExitMark) {
                    const { size: exitSize, priceText: exitPriceText } = getAdaptiveMarkerProps(exitPrice);

                    // Exit marker - force text visibility with multiple strategies
                    const exitMarker = {
                        time: exitTimestamp,
                        position: 'aboveBar',
                        color: '#ffeb3b', // Yellow color for exit marks
                        shape: 'square',
                        size: exitSize
                    };

                    // Strategy 1: Always include text
                    exitMarker.text = `EXIT @ ${exitPriceText}`;

                    // Strategy 2: For very zoomed out views, use minimal but visible text
                    if (barSpacing < 1) {
                        exitMarker.text = 'X'; // Single letter for extreme zoom
                    } else if (barSpacing < 2) {
                        exitMarker.text = 'EXIT'; // Just EXIT
                    }

                    markers.push(exitMarker);
                }
            }
        });

        this.chart.candlestickSeries.setMarkers(markers);

        // Debug: Log marker details for troubleshooting
        console.log(`📊 Refreshed chart markers: ${markers.length} total`);
        markers.forEach((marker, index) => {
            const markerDate = new Date(marker.time * 1000);
            console.log(`   Marker ${index + 1}: ${marker.text} (time: ${marker.time}, date: ${markerDate.toISOString()}, size: ${marker.size}, color: ${marker.color})`);
        });

        // Debug: Check for missing markers by comparing with stored marks
        const storedMarkCount = this.marks.size;
        if (markers.length !== storedMarkCount) {
            console.warn(`⚠️  Marker count mismatch: ${markers.length} displayed vs ${storedMarkCount} stored`);

            // Check which marks are missing from display
            this.marks.forEach((mark, id) => {
                const markTimestamp = mark.entry_timestamp || mark.timestamp;
                const markDate = new Date(markTimestamp * 1000);
                const hasMarker = markers.some(m => Math.abs(m.time - markTimestamp) < 60); // Within 1 minute

                if (!hasMarker) {
                    console.warn(`   Missing marker for mark ${id}: ${markDate.toISOString()} (timestamp: ${markTimestamp})`);
                }
            });
        }

        // Create trade lines after setting markers
        this.createTradeLines();
    }

    /**
     * Debug function to check marker visibility and properties
     */
    debugMarkers() {
        console.log('=== MARKER DEBUG INFO ===');

        if (!this.chart.candlestickSeries) {
            console.log('❌ No candlestick series available');
            return;
        }

        const chartMarkers = this.chart.candlestickSeries.markers() || [];
        const timeScale = this.chart.chart.timeScale();
        const barSpacing = timeScale.options().barSpacing || 6;

        console.log(`📊 Chart Info:`);
        console.log(`   Bar spacing: ${barSpacing}`);
        console.log(`   Total markers on chart: ${chartMarkers.length}`);
        console.log(`   Total marks in storage: ${this.marks.size}`);

        console.log(`📋 Chart Markers:`);
        chartMarkers.forEach((marker, index) => {
            console.log(`   ${index + 1}. ${marker.text || 'NO TEXT'} (size: ${marker.size}, color: ${marker.color}, shape: ${marker.shape})`);
        });

        console.log(`📋 Stored Marks:`);
        this.marks.forEach((mark, id) => {
            const type = mark.mark_type || 'ENTRY';
            const price = mark.entry_price || mark.price || 0;
            console.log(`   ${id}: ${type} @ $${price.toFixed(2)} (side: ${mark.side || 'unknown'})`);
        });

        console.log('=== END DEBUG INFO ===');
    }

    /**
     * Create line connections between entry and exit marks
     */
    createTradeLines() {
        // Clear existing trade lines
        this.clearTradeLines();

        // Group marks by type
        const entryMarks = [];
        const exitMarks = [];

        this.marks.forEach(mark => {
            if (mark.mark_type === 'ENTRY') {
                entryMarks.push(mark);
            } else if (mark.mark_type === 'EXIT') {
                exitMarks.push(mark);
            }
        });

        console.log('🔍 DEBUG: Creating trade lines - Entry marks:', entryMarks.length, 'Exit marks:', exitMarks.length);

        // Link entry and exit marks using linked_trade_id
        entryMarks.forEach(entryMark => {
            // Find exit mark with matching linked_trade_id
            const matchingExit = exitMarks.find(exitMark =>
                exitMark.linked_trade_id === entryMark.id &&
                exitMark.symbol === entryMark.symbol &&
                exitMark.timeframe === entryMark.timeframe
            );

            if (matchingExit) {
                console.log('🔗 DEBUG: Linking entry', entryMark.id, 'with exit', matchingExit.id, 'via linked_trade_id:', matchingExit.linked_trade_id);
                this.createTradeLine(entryMark, matchingExit);
            } else {
                console.log('⚠️ DEBUG: No matching exit found for entry', entryMark.id);
            }
        });
    }

    /**
     * Create a line between entry and exit marks
     */
    createTradeLine(entryMark, exitMark) {
        try {
            const entryPrice = entryMark.price;
            const exitPrice = exitMark.price;
            const entrySide = entryMark.entry_side;

            // Convert timestamps to Unix timestamps (seconds since epoch) for TradingView
            let entryTime, exitTime;

            if (typeof entryMark.timestamp === 'string') {
                entryTime = Math.floor(new Date(entryMark.timestamp).getTime() / 1000);
            } else if (typeof entryMark.timestamp === 'number') {
                entryTime = entryMark.timestamp > 1000000000000 ?
                    Math.floor(entryMark.timestamp / 1000) :
                    Math.floor(entryMark.timestamp);
            } else {
                throw new Error('Invalid entry timestamp format');
            }

            if (typeof exitMark.timestamp === 'string') {
                exitTime = Math.floor(new Date(exitMark.timestamp).getTime() / 1000);
            } else if (typeof exitMark.timestamp === 'number') {
                exitTime = exitMark.timestamp > 1000000000000 ?
                    Math.floor(exitMark.timestamp / 1000) :
                    Math.floor(exitMark.timestamp);
            } else {
                throw new Error('Invalid exit timestamp format');
            }

            // Align both timestamps with chart data
            entryTime = this.alignTimestampWithChart(entryTime);
            exitTime = this.alignTimestampWithChart(exitTime);

            // Determine line color and style based on entry side and profit/loss
            const isProfit = (entrySide === 'BUY' && exitPrice > entryPrice) ||
                           (entrySide === 'SELL' && exitPrice < entryPrice);

            // Base color on entry side, but adjust opacity based on profit/loss
            let lineColor, lineWidth;
            if (entrySide === 'BUY') {
                lineColor = isProfit ? '#4caf50' : '#4caf5080'; // Solid green for profit, transparent for loss
                lineWidth = isProfit ? 3 : 2; // Thicker line for profitable trades
            } else {
                lineColor = isProfit ? '#f44336' : '#f4433680'; // Solid red for profit, transparent for loss
                lineWidth = isProfit ? 3 : 2; // Thicker line for profitable trades
            }

            const profitPct = entrySide === 'BUY' ?
                ((exitPrice - entryPrice) / entryPrice * 100) :
                ((entryPrice - exitPrice) / entryPrice * 100);

            console.log('🔍 DEBUG: Creating trade line:', {
                entryPrice, exitPrice, entryTime, exitTime, entrySide, lineColor, lineWidth,
                isProfit, profitPct: profitPct.toFixed(2) + '%',
                entryTimestamp: entryMark.timestamp,
                exitTimestamp: exitMark.timestamp
            });

            // Create a line series for this trade
            const lineSeries = this.chart.chart.addLineSeries({
                color: lineColor,
                lineWidth: lineWidth,
                lineStyle: 0, // Solid line
                crosshairMarkerVisible: false,
                lastValueVisible: false,
                priceLineVisible: false,
                title: `${entrySide} Trade: ${profitPct.toFixed(2)}%`, // Tooltip title
            });

            // Set the line data (two points: entry and exit)
            lineSeries.setData([
                { time: entryTime, value: entryPrice },
                { time: exitTime, value: exitPrice }
            ]);

            // Store the line series with metadata for cleanup and reference
            const lineId = `${entryMark.id}-${exitMark.id}`;
            this.tradeLines.set(lineId, {
                series: lineSeries,
                entryMark: entryMark,
                exitMark: exitMark,
                profitPct: profitPct,
                isProfit: isProfit
            });

            console.log('✅ Trade line created successfully:', lineId, `(${isProfit ? 'PROFIT' : 'LOSS'}: ${profitPct.toFixed(2)}%)`);

        } catch (error) {
            console.error('Error creating trade line:', error);
        }
    }

    /**
     * Clear all existing trade lines
     */
    clearTradeLines() {
        this.tradeLines.forEach((lineData, lineId) => {
            try {
                // Handle both old format (direct series) and new format (object with series)
                const series = lineData.series || lineData;
                this.chart.chart.removeSeries(series);
                console.log('🗑️ Removed trade line:', lineId);
            } catch (error) {
                console.error('Error removing trade line:', lineId, error);
            }
        });
        this.tradeLines.clear();
    }

    /**
     * Get trade statistics for display
     */
    getTradeStatistics() {
        const stats = {
            totalTrades: this.tradeLines.size,
            profitableTrades: 0,
            losingTrades: 0,
            totalProfitPct: 0,
            bestTrade: null,
            worstTrade: null
        };

        this.tradeLines.forEach((lineData, lineId) => {
            if (lineData.profitPct !== undefined) {
                if (lineData.isProfit) {
                    stats.profitableTrades++;
                } else {
                    stats.losingTrades++;
                }

                stats.totalProfitPct += lineData.profitPct;

                if (!stats.bestTrade || lineData.profitPct > stats.bestTrade.profitPct) {
                    stats.bestTrade = { lineId, ...lineData };
                }

                if (!stats.worstTrade || lineData.profitPct < stats.worstTrade.profitPct) {
                    stats.worstTrade = { lineId, ...lineData };
                }
            }
        });

        stats.winRate = stats.totalTrades > 0 ? (stats.profitableTrades / stats.totalTrades * 100) : 0;
        stats.avgProfitPct = stats.totalTrades > 0 ? (stats.totalProfitPct / stats.totalTrades) : 0;

        return stats;
    }

    async loadExistingMarks() {
        try {
            const response = await fetch('/api/v1/marks');
            const result = await response.json();

            if (result.success && result.data) {
                console.log('📥 Loading marks from database:', result.data.length);

                // First, add all marks to the collection
                result.data.forEach(mark => {
                    this.marks.set(mark.id, mark);
                });

                // Then, process exits and link them to their entries
                this.linkExitsToEntries();

                this.refreshChartMarkers();
                this.updateSidebar();

                console.log('✅ Marks loaded and processed:', this.marks.size);
            }
        } catch (error) {
            console.error('Error loading existing marks:', error);
        }
    }

    linkExitsToEntries() {
        console.log('🔗 Linking exits to entries...');

        // Find all exit marks and link them to their entries
        this.marks.forEach((mark, id) => {
            if (mark.mark_type === 'EXIT' && mark.linked_trade_id) {
                const entryId = mark.linked_trade_id;
                const entryMark = this.marks.get(entryId);

                if (entryMark) {
                    // Update the entry mark with exit information
                    entryMark.exit_timestamp = mark.timestamp;
                    entryMark.exit_price = mark.price;
                    entryMark.exit_notes = mark.notes;
                    entryMark.exit_ohlcv_data = mark.ohlcv_snapshot;
                    entryMark.exit_indicator_data = mark.indicator_snapshot;
                    entryMark.status = 'closed';
                    entryMark.linked_exit_id = mark.id;

                    // Update the entry in the collection
                    this.marks.set(entryId, entryMark);

                    console.log(`✅ Linked exit ${id} to entry ${entryId}`);
                } else {
                    console.warn(`⚠️ Exit ${id} references non-existent entry ${entryId}`);
                }
            }
        });

        console.log('🔗 Exit linking completed');
    }

    initializeDragAndDrop() {
        if (!this.chart || !this.chart.chart) {
            console.warn('Chart not available for drag and drop initialization');
            return;
        }

        console.log('🎯 Initializing drag and drop functionality');

        // Subscribe to mouse events on the chart
        let mouseDownTime = 0;
        let mouseDownPosition = null;

        this.chart.chart.subscribeClick((param) => {
            if (this.isDragging) {
                this.handleDragEnd(param);
            } else if (!this.isMarkingMode) {
                // Only check for drag start if not in marking mode
                this.handlePotentialDragStart(param);
            }
        });

        // Add mouse move listener to the chart container
        if (this.chart.container) {
            this.chart.container.addEventListener('mousemove', (event) => {
                if (this.isDragging) {
                    this.handleDragMove(event);
                } else {
                    this.handleMouseHover(event);
                }
            });

            this.chart.container.addEventListener('mouseup', (event) => {
                if (this.isDragging) {
                    this.handleDragEnd(null, event);
                }
            });

            // Add context menu for delete functionality
            this.chart.container.addEventListener('contextmenu', (event) => {
                event.preventDefault();
                this.handleRightClick(event);
            });
        }

        console.log('✅ Drag and drop initialized');
    }

    handleMouseHover(event) {
        if (this.isMarkingMode || this.isDragging) return;

        // Get mouse position relative to chart
        const rect = this.chart.container.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        // Convert to chart coordinates
        const time = this.chart.chart.timeScale().coordinateToTime(x);
        const price = this.chart.candlestickSeries.coordinateToPrice(y);

        if (time && price) {
            const nearestMark = this.findNearestMark(time, null);
            if (nearestMark) {
                // Show drag cursor when hovering over a mark
                this.chart.container.style.cursor = 'grab';

                // Show tooltip with mark info
                this.showMarkTooltip(nearestMark, event.clientX, event.clientY);
            } else {
                // Reset cursor and hide tooltip
                this.chart.container.style.cursor = 'default';
                this.hideMarkTooltip();
            }
        }
    }

    showMarkTooltip(mark, x, y) {
        // Remove existing tooltip
        this.hideMarkTooltip();

        const tooltip = document.createElement('div');
        tooltip.className = 'mark-tooltip';
        tooltip.style.cssText = `
            position: fixed;
            top: ${y - 40}px;
            left: ${x + 10}px;
            background: rgba(42, 46, 57, 0.95);
            border: 1px solid #454950;
            border-radius: 4px;
            padding: 8px 12px;
            z-index: 10000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 12px;
            color: #b2b5be;
            pointer-events: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        `;

        const markType = mark.mark_type || 'ENTRY';
        const price = mark.price || mark.entry_price || 0;
        const side = mark.side || mark.entry_side || 'buy';

        tooltip.innerHTML = `
            <div style="font-weight: 600; color: ${this.getMarkColor(mark)};">
                ${markType} - ${side.toUpperCase()}
            </div>
            <div>Price: $${price.toFixed(2)}</div>
            <div style="font-size: 11px; color: #888; margin-top: 4px;">
                Drag to move • Right-click to delete
            </div>
        `;

        document.body.appendChild(tooltip);
        this.currentTooltip = tooltip;
    }

    hideMarkTooltip() {
        if (this.currentTooltip) {
            this.currentTooltip.remove();
            this.currentTooltip = null;
        }
    }

    handlePotentialDragStart(param) {
        if (!param || !param.point || !param.time) return;

        // Check if click is near any existing marker
        const clickedMark = this.findNearestMark(param.time, param.seriesData);

        if (clickedMark && !this.isMarkingMode) {
            console.log('🎯 Starting drag on mark:', clickedMark.id);
            this.startDrag(clickedMark, param);
        }
    }

    startDrag(mark, param) {
        this.isDragging = true;
        this.draggedMark = mark;
        this.dragStartPosition = {
            time: param.time,
            price: param.seriesData ? param.seriesData.get(this.chart.candlestickSeries) : null
        };

        // Hide tooltip during drag
        this.hideMarkTooltip();

        // Show visual feedback
        this.showDragFeedback(mark);

        console.log('🎯 Drag started for mark:', mark.id);
    }

    findNearestMark(clickTime, seriesData) {
        let nearestMark = null;
        let minDistance = Infinity;
        const timeThreshold = 300; // 5 minutes in seconds (for 15m timeframe)

        this.marks.forEach((mark, id) => {
            const markTime = mark.timestamp || mark.entry_timestamp;
            if (!markTime) return;

            const distance = Math.abs(markTime - clickTime);
            if (distance < timeThreshold && distance < minDistance) {
                minDistance = distance;
                nearestMark = { ...mark, id: id };
            }
        });

        return nearestMark;
    }



    showDragFeedback(mark) {
        // Create a ghost price line to show current drag position
        const markPrice = mark.price || mark.entry_price;
        const color = this.getMarkColor(mark);

        this.dragGhostLine = this.chart.candlestickSeries.createPriceLine({
            price: markPrice,
            color: color,
            lineWidth: 2,
            lineStyle: 1, // Dashed
            axisLabelVisible: true,
            title: `Dragging ${mark.mark_type || 'ENTRY'} @ $${markPrice.toFixed(2)}`
        });

        // Add visual indicator to chart container
        this.addDragCursor();
    }

    addDragCursor() {
        if (this.chart.container) {
            this.chart.container.style.cursor = 'grabbing';
        }
    }

    removeDragCursor() {
        if (this.chart.container) {
            this.chart.container.style.cursor = 'default';
        }
    }

    getMarkColor(mark) {
        if (mark.mark_type === 'EXIT') {
            return '#ff9800'; // Orange for exits
        }

        const side = mark.side || mark.entry_side || 'buy';
        return side.toLowerCase() === 'buy' ? '#4caf50' : '#f44336';
    }

    handleDragMove(event) {
        if (!this.isDragging || !this.draggedMark) return;

        // Get mouse position relative to chart
        const rect = this.chart.container.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        // Convert mouse position to price
        const price = this.chart.candlestickSeries.coordinateToPrice(y);

        if (price && this.dragGhostLine) {
            // Update ghost line position
            this.dragGhostLine.applyOptions({
                price: price,
                title: `Dragging ${this.draggedMark.mark_type || 'ENTRY'} @ $${price.toFixed(2)}`
            });
        }
    }

    handleDragEnd(param, mouseEvent) {
        if (!this.isDragging || !this.draggedMark) return;

        console.log('🎯 Drag end for mark:', this.draggedMark.id);

        let newPrice = null;
        let newTime = null;

        if (param && param.time && param.seriesData) {
            // Use chart click data
            newTime = param.time;
            const candleData = param.seriesData.get(this.chart.candlestickSeries);
            newPrice = candleData ? candleData.close : null;
        } else if (mouseEvent) {
            // Use mouse position
            const rect = this.chart.container.getBoundingClientRect();
            const y = mouseEvent.clientY - rect.top;
            newPrice = this.chart.candlestickSeries.coordinateToPrice(y);
            newTime = this.draggedMark.timestamp || this.draggedMark.entry_timestamp; // Keep original time
        }

        if (newPrice) {
            this.updateMarkPosition(this.draggedMark, newTime, newPrice);
        }

        this.cleanupDrag();
    }

    async updateMarkPosition(mark, newTime, newPrice) {
        console.log('📝 Updating mark position:', {
            markId: mark.id,
            oldPrice: mark.price || mark.entry_price,
            newPrice: newPrice,
            newTime: newTime
        });

        try {
            // Update mark data
            const updatedMark = { ...mark };

            if (mark.mark_type === 'EXIT') {
                updatedMark.price = newPrice;
                if (newTime) updatedMark.timestamp = newTime;
            } else {
                // Entry mark
                updatedMark.entry_price = newPrice;
                updatedMark.price = newPrice;
                if (newTime) {
                    updatedMark.entry_timestamp = newTime;
                    updatedMark.timestamp = newTime;
                }
            }

            // Update in database
            const updatePayload = {};
            if (newPrice !== null && newPrice !== undefined) {
                updatePayload.price = newPrice;
            }
            if (newTime !== null && newTime !== undefined) {
                updatePayload.timestamp = Math.floor(newTime);
            }

            console.log('📤 Sending update payload:', updatePayload);

            const response = await fetch(`/api/v1/marks/${mark.id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updatePayload)
            });

            if (response.ok) {
                const result = await response.json();
                console.log('✅ Mark updated in database:', result);

                // Update local collection
                this.marks.set(mark.id, updatedMark);

                // Refresh chart and sidebar
                this.refreshChartMarkers();
                this.updateSidebar();

                console.log('✅ Mark position updated successfully');
            } else {
                const errorText = await response.text();
                console.error('❌ Failed to update mark in database:', {
                    status: response.status,
                    statusText: response.statusText,
                    error: errorText
                });
                alert(`Failed to update mark position: ${response.status} ${response.statusText}\n${errorText}`);
            }

        } catch (error) {
            console.error('❌ Error updating mark position:', error);
            alert('Error updating mark position. Please try again.');
        }
    }

    cleanupDrag() {
        this.isDragging = false;
        this.draggedMark = null;
        this.dragStartPosition = null;

        // Remove ghost line
        if (this.dragGhostLine) {
            this.chart.candlestickSeries.removePriceLine(this.dragGhostLine);
            this.dragGhostLine = null;
        }

        // Remove drag cursor
        this.removeDragCursor();

        // Clear feedback elements
        this.dragFeedbackElements.forEach(element => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
        });
        this.dragFeedbackElements = [];

        console.log('🧹 Drag cleanup completed');
    }

    handleRightClick(event) {
        // Find mark near right-click position
        const rect = this.chart.container.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        // Convert to chart coordinates
        const time = this.chart.chart.timeScale().coordinateToTime(x);
        const price = this.chart.candlestickSeries.coordinateToPrice(y);

        if (time && price) {
            const nearestMark = this.findNearestMark(time, null);
            if (nearestMark) {
                this.showMarkContextMenu(nearestMark, event.clientX, event.clientY);
            }
        }
    }

    showMarkContextMenu(mark, x, y) {
        // Remove existing context menu
        this.removeContextMenu();

        // Create context menu
        const menu = document.createElement('div');
        menu.className = 'mark-context-menu';
        menu.style.cssText = `
            position: fixed;
            top: ${y}px;
            left: ${x}px;
            background: #2a2e39;
            border: 1px solid #454950;
            border-radius: 4px;
            padding: 8px 0;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 13px;
            min-width: 120px;
        `;

        // Add menu items
        const deleteItem = document.createElement('div');
        deleteItem.className = 'context-menu-item';
        deleteItem.textContent = 'Delete Mark';
        deleteItem.style.cssText = `
            padding: 8px 16px;
            color: #ff6b6b;
            cursor: pointer;
            transition: background-color 0.2s;
        `;
        deleteItem.addEventListener('mouseenter', () => {
            deleteItem.style.backgroundColor = '#363c4e';
        });
        deleteItem.addEventListener('mouseleave', () => {
            deleteItem.style.backgroundColor = 'transparent';
        });
        deleteItem.addEventListener('click', () => {
            this.deleteMark(mark);
            this.removeContextMenu();
        });

        menu.appendChild(deleteItem);
        document.body.appendChild(menu);

        // Close menu when clicking elsewhere
        setTimeout(() => {
            document.addEventListener('click', this.removeContextMenu.bind(this), { once: true });
        }, 100);
    }

    removeContextMenu() {
        const existingMenu = document.querySelector('.mark-context-menu');
        if (existingMenu) {
            existingMenu.remove();
        }
    }

    /**
     * Align timestamp with chart data to ensure markers appear correctly on all timeframes
     * This is crucial for intraday intervals where exact timestamp matching is required
     */
    alignTimestampWithChart(timestamp) {
        if (!this.chart || !this.chart.candlestickSeries) {
            return this.roundTimestampToTimeframe(timestamp);
        }

        try {
            const timeframe = this.getCurrentTimeframe();

            // First, round timestamp to timeframe boundary
            const roundedTimestamp = this.roundTimestampToTimeframe(timestamp);

            // Get current chart data
            const chartData = this.chart.candlestickSeries.data();
            if (!chartData || chartData.length === 0) {
                console.log(`📊 No chart data available, using rounded timestamp: ${roundedTimestamp}`);
                return roundedTimestamp;
            }

            // Find the closest timestamp in chart data
            let closestTime = roundedTimestamp;
            let minDiff = Infinity;

            for (const dataPoint of chartData) {
                const timeDiff = Math.abs(dataPoint.time - roundedTimestamp);
                if (timeDiff < minDiff) {
                    minDiff = timeDiff;
                    closestTime = dataPoint.time;
                }
            }

            // Define reasonable alignment tolerances based on timeframe
            let maxDiff;
            if (timeframe.includes('d')) {
                maxDiff = 86400; // 1 day for daily
            } else if (timeframe.includes('w') || timeframe.includes('M')) {
                maxDiff = 604800; // 1 week for weekly/monthly
            } else if (timeframe.includes('h')) {
                maxDiff = 7200; // 2 hours for hourly
            } else {
                maxDiff = 3600; // 1 hour for intraday
            }

            console.log(`🎯 Timestamp alignment: ${timestamp} -> rounded: ${roundedTimestamp} -> closest: ${closestTime} (diff: ${minDiff}s, maxDiff: ${maxDiff}s, timeframe: ${timeframe})`);

            if (minDiff <= maxDiff) {
                console.log(`✅ Using chart-aligned timestamp: ${closestTime}`);
                return closestTime;
            } else {
                console.log(`⚠️  Using rounded timestamp (chart diff too large): ${roundedTimestamp}`);
                return roundedTimestamp;
            }
        } catch (error) {
            console.warn('Error aligning timestamp with chart:', error);
            return this.roundTimestampToTimeframe(timestamp);
        }
    }

    /**
     * Round timestamp to the nearest timeframe boundary
     * This ensures markers align with candlestick intervals
     */
    roundTimestampToTimeframe(timestamp) {
        const timeframe = this.getCurrentTimeframe();

        // Get interval in seconds
        const intervalSeconds = this.getTimeframeSeconds(timeframe);

        // Round timestamp to nearest interval boundary
        const rounded = Math.floor(timestamp / intervalSeconds) * intervalSeconds;

        console.log(`🔄 Rounded timestamp for ${timeframe}: ${timestamp} -> ${rounded} (interval: ${intervalSeconds}s)`);
        return rounded;
    }

    /**
     * Get timeframe interval in seconds
     */
    getTimeframeSeconds(timeframe) {
        const intervalMap = {
            '1m': 60, '3m': 180, '5m': 300, '15m': 900, '30m': 1800,
            '1h': 3600, '2h': 7200, '4h': 14400, '6h': 21600, '8h': 28800, '12h': 43200,
            '1d': 86400, '3d': 259200, '1w': 604800, '1M': 2592000
        };
        return intervalMap[timeframe] || 900; // Default to 15m if unknown
    }

    /**
     * Debug method to check timestamp alignment issues
     */
    debugTimestampAlignment() {
        if (!this.chart || !this.chart.candlestickSeries) {
            console.log('❌ No chart or candlestick series available');
            return;
        }

        const chartData = this.chart.candlestickSeries.data();
        const timeframe = this.getCurrentTimeframe();

        console.log('🔍 Chart Data Debug Info:');
        console.log(`Timeframe: ${timeframe}`);
        console.log(`Chart data points: ${chartData.length}`);

        if (chartData.length > 0) {
            console.log(`First timestamp: ${chartData[0].time} (${new Date(chartData[0].time * 1000).toISOString()})`);
            console.log(`Last timestamp: ${chartData[chartData.length - 1].time} (${new Date(chartData[chartData.length - 1].time * 1000).toISOString()})`);

            // Show sample of timestamps
            const sampleSize = Math.min(5, chartData.length);
            console.log(`Sample timestamps:`);
            for (let i = 0; i < sampleSize; i++) {
                const point = chartData[i];
                console.log(`  ${point.time} -> ${new Date(point.time * 1000).toISOString()}`);
            }
        }

        console.log('🎯 Current marks:');
        this.marks.forEach((mark, id) => {
            const timestamp = mark.entry_timestamp || mark.timestamp;
            console.log(`  Mark ${id}: ${timestamp} -> ${new Date(timestamp * 1000).toISOString()}`);
        });
    }

    async deleteMark(mark) {
        const confirmMessage = `Are you sure you want to delete this ${mark.mark_type || 'ENTRY'} mark?\n\n` +
                              `Price: $${(mark.price || mark.entry_price || 0).toFixed(2)}\n` +
                              `Time: ${new Date((mark.timestamp || mark.entry_timestamp) * 1000).toLocaleString()}`;

        if (!confirm(confirmMessage)) {
            return;
        }

        try {
            console.log('🗑️ Deleting mark:', mark.id);

            // Delete from database
            const response = await fetch(`/api/v1/marks/${mark.id}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                console.log('✅ Mark deleted from database');

                // If this is an exit mark, also clean up the linked entry
                if (mark.mark_type === 'EXIT' && mark.linked_trade_id) {
                    const linkedEntry = this.marks.get(mark.linked_trade_id);
                    if (linkedEntry) {
                        // Remove exit data from entry
                        delete linkedEntry.exit_timestamp;
                        delete linkedEntry.exit_price;
                        delete linkedEntry.exit_notes;
                        delete linkedEntry.exit_ohlcv_data;
                        delete linkedEntry.exit_indicator_data;
                        delete linkedEntry.status;
                        delete linkedEntry.linked_exit_id;

                        this.marks.set(mark.linked_trade_id, linkedEntry);
                        console.log('🔗 Cleaned up linked entry:', mark.linked_trade_id);
                    }
                }

                // If this is an entry mark, also delete any linked exits
                if (mark.mark_type === 'ENTRY' || !mark.mark_type) {
                    this.marks.forEach((otherMark, otherId) => {
                        if (otherMark.mark_type === 'EXIT' && otherMark.linked_trade_id === mark.id) {
                            this.marks.delete(otherId);
                            console.log('🗑️ Deleted linked exit mark:', otherId);
                        }
                    });
                }

                // Remove from local collection
                this.marks.delete(mark.id);

                // Remove any trade lines associated with this mark
                this.removeTradeLines(mark.id);

                // Refresh chart and sidebar
                this.refreshChartMarkers();
                this.updateSidebar();

                console.log('✅ Mark deleted successfully');
            } else {
                console.error('❌ Failed to delete mark from database:', response.status);
                alert('Failed to delete mark. Please try again.');
            }

        } catch (error) {
            console.error('❌ Error deleting mark:', error);
            alert('Error deleting mark. Please try again.');
        }
    }



    removeTradeLines(markId) {
        // Remove any trade lines associated with this mark
        this.tradeLines.forEach((line, key) => {
            if (key.includes(markId.toString())) {
                try {
                    this.chart.chart.removeSeries(line);
                    this.tradeLines.delete(key);
                    console.log('🗑️ Removed trade line:', key);
                } catch (error) {
                    console.warn('⚠️ Error removing trade line:', error);
                }
            }
        });
    }

    updateSidebar() {
        this.updateMarksList();
        this.updateStatistics();
    }

    updateMarksList() {
        const container = document.getElementById('active-marks-list');

        if (this.marks.size === 0) {
            container.innerHTML = '<div class="no-marks">No active marks</div>';
            return;
        }

        // Group marks into trade pairs
        const tradePairs = new Map();
        const standaloneMarks = [];

        this.marks.forEach(mark => {
            if (mark.mark_type === 'EXIT' && mark.linked_trade_id) {
                // This is an exit linked to an entry
                if (!tradePairs.has(mark.linked_trade_id)) {
                    tradePairs.set(mark.linked_trade_id, { entry: null, exit: null });
                }
                tradePairs.get(mark.linked_trade_id).exit = mark;
            } else if (mark.mark_type === 'ENTRY' || !mark.mark_type) {
                // This is an entry mark
                if (!tradePairs.has(mark.id)) {
                    tradePairs.set(mark.id, { entry: null, exit: null });
                }
                tradePairs.get(mark.id).entry = mark;

                // Check if this entry has exit data (legacy format)
                if (mark.exit_price && mark.exit_timestamp) {
                    tradePairs.get(mark.id).exit = {
                        price: mark.exit_price,
                        timestamp: mark.exit_timestamp,
                        quantity: mark.quantity
                    };
                }
            } else {
                // Standalone marks (like unlinked exits)
                standaloneMarks.push(mark);
            }
        });

        let html = '';

        // Display trade pairs
        tradePairs.forEach((pair, tradeId) => {
            const entry = pair.entry;
            const exit = pair.exit;

            if (!entry) return; // Skip if no entry found

            const side = entry.side || (entry.entry_side ? entry.entry_side.toLowerCase() : 'buy');
            const entryPrice = entry.entry_price || entry.price || 0;
            const exitPrice = exit ? (exit.price || 0) : null;
            const quantity = entry.quantity || 1;

            let pnl = 'Open';
            let pnlColor = '#888';
            let status = 'Open';

            if (exitPrice) {
                const profit = (exitPrice - entryPrice) * (side === 'buy' ? 1 : -1) * quantity;
                pnl = profit.toFixed(2);
                pnlColor = parseFloat(pnl) >= 0 ? '#4caf50' : '#f44336';
                status = 'Closed';
            }

            html += `
                <div class="trade-pair">
                    <div class="trade-header">
                        <span style="color: ${side === 'buy' ? '#4caf50' : '#f44336'};">${side.toUpperCase()}</span>
                        <span style="color: ${pnlColor}; font-size: 9px;">${status}</span>
                    </div>
                    <div class="trade-details">
                        <div class="entry-info">
                            <span class="mark-side ${side}">${side}</span>
                            <span>$${entryPrice.toFixed(2)}</span>
                            <span style="color: #888;">(${quantity})</span>
                        </div>
                        <div class="exit-info">
                            ${exitPrice ? `
                                <span class="mark-side exit" style="background: #ffeb3b; color: #000;">EXIT</span>
                                <span>$${exitPrice.toFixed(2)}</span>
                            ` : '<span style="color: #888;">No Exit</span>'}
                        </div>
                    </div>
                    <div style="font-size: 9px; color: ${pnlColor}; margin-top: 2px; text-align: center;">
                        P&L: ${pnl === 'Open' ? pnl : '$' + pnl}
                    </div>
                </div>
            `;
        });

        // Display standalone marks
        standaloneMarks.forEach(mark => {
            if (mark.mark_type === 'EXIT') {
                const exitPrice = mark.price || 0;
                const quantity = mark.quantity || 1;

                html += `
                    <div class="trade-pair">
                        <div class="trade-header">
                            <span style="color: #ffeb3b;">EXIT</span>
                            <span style="color: #888; font-size: 9px;">Standalone</span>
                        </div>
                        <div class="trade-details">
                            <div class="exit-info">
                                <span class="mark-side exit" style="background: #ffeb3b; color: #000;">EXIT</span>
                                <span>$${exitPrice.toFixed(2)}</span>
                                <span style="color: #888;">(${quantity})</span>
                            </div>
                        </div>
                    </div>
                `;
            }
        });

        container.innerHTML = html || '<div class="no-marks">No active marks</div>';
    }

    updateStatistics() {
        const allMarks = Array.from(this.marks.values());

        // Separate entry and exit marks
        const entryMarks = allMarks.filter(m => m.mark_type !== 'EXIT');
        const exitMarks = allMarks.filter(m => m.mark_type === 'EXIT');

        const totalEntries = entryMarks.length;
        const totalExits = exitMarks.length;
        const openPositions = entryMarks.filter(m => m.status === 'open' || !m.exit_price).length;
        const closedTrades = entryMarks.filter(m => m.status === 'closed' || m.exit_price).length;

        const closedMarks = entryMarks.filter(m => m.status === 'closed' || m.exit_price);
        const winningTrades = closedMarks.filter(m => {
            const entryPrice = m.entry_price || m.price || 0;
            const exitPrice = m.exit_price || 0;
            const side = m.side || (m.entry_side ? m.entry_side.toLowerCase() : 'buy');
            const pnl = (exitPrice - entryPrice) * (side === 'buy' ? 1 : -1);
            return pnl > 0;
        }).length;

        const winRate = closedTrades > 0 ? ((winningTrades / closedTrades) * 100).toFixed(1) : 0;

        // Get trade line statistics
        const tradeStats = this.getTradeStatistics();

        // Update display elements
        const totalElement = document.getElementById('total-entries');
        if (totalElement) {
            totalElement.textContent = `${totalEntries} entries, ${totalExits} exits`;
        }

        const openElement = document.getElementById('open-positions');
        if (openElement) {
            openElement.textContent = openPositions;
        }

        const closedElement = document.getElementById('closed-trades');
        if (closedElement) {
            closedElement.textContent = `${closedTrades} (${tradeStats.totalTrades} linked)`;
        }

        const winRateElement = document.getElementById('win-rate');
        if (winRateElement) {
            const displayRate = tradeStats.totalTrades > 0 ? tradeStats.winRate.toFixed(1) : winRate;
            winRateElement.textContent = displayRate + '%';
        }

        // Add trade line statistics display
        this.updateTradeLineStats(tradeStats);
    }

    updateTradeLineStats(stats) {
        // Find or create trade lines stats container
        let statsContainer = document.getElementById('trade-lines-stats');
        if (!statsContainer) {
            // Try to add it after the existing stats
            const winRateElement = document.getElementById('win-rate');
            if (winRateElement && winRateElement.parentElement) {
                statsContainer = document.createElement('div');
                statsContainer.id = 'trade-lines-stats';
                statsContainer.style.cssText = 'margin-top: 10px; padding-top: 10px; border-top: 1px solid #444; font-size: 12px;';
                winRateElement.parentElement.appendChild(statsContainer);
            }
        }

        if (statsContainer && stats.totalTrades > 0) {
            statsContainer.innerHTML = `
                <div style="color: #26a69a; font-weight: bold; margin-bottom: 5px;">📈 Trade Lines</div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
                    <span>Connected Trades:</span>
                    <span style="color: #2196f3;">${stats.totalTrades}</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
                    <span>Win Rate:</span>
                    <span style="color: ${stats.winRate >= 50 ? '#4caf50' : '#f44336'};">${stats.winRate.toFixed(1)}%</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
                    <span>Avg Profit:</span>
                    <span style="color: ${stats.avgProfitPct >= 0 ? '#4caf50' : '#f44336'};">${stats.avgProfitPct.toFixed(2)}%</span>
                </div>
                ${stats.bestTrade ? `
                <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
                    <span>Best Trade:</span>
                    <span style="color: #4caf50;">+${stats.bestTrade.profitPct.toFixed(2)}%</span>
                </div>
                ` : ''}
                ${stats.worstTrade ? `
                <div style="display: flex; justify-content: space-between;">
                    <span>Worst Trade:</span>
                    <span style="color: #f44336;">${stats.worstTrade.profitPct.toFixed(2)}%</span>
                </div>
                ` : ''}
            `;
        } else if (statsContainer) {
            statsContainer.innerHTML = '<div style="color: #888; font-size: 11px;">No connected trades</div>';
        }
    }

    enableMarkingMode() {
        console.log('enableMarkingMode called');
        this.isMarkingMode = true;
        console.log('Marking mode enabled - isMarkingMode:', this.isMarkingMode);
        console.log('Chart container available:', !!this.chart?.container);

        // Update status
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.textContent = 'Marking mode active - Click to add entries, right-click for exits';
            statusElement.className = 'status warning';
        }
    }

    disableMarkingMode() {
        console.log('disableMarkingMode called');
        this.isMarkingMode = false;
        console.log('Marking mode disabled - isMarkingMode:', this.isMarkingMode);

        // Update status
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.textContent = 'Chart ready';
            statusElement.className = 'status info';
        }
    }

    async exportMarks() {
        try {
            const response = await fetch('/api/v1/marks/export');
            const blob = await response.blob();
            
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `marks_export_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Error exporting marks:', error);
            alert('Error exporting marks');
        }
    }

    async clearAllMarks() {
        if (!confirm('Are you sure you want to clear all marks? This action cannot be undone.')) {
            return;
        }

        try {
            const response = await fetch('/api/v1/marks', {
                method: 'DELETE'
            });

            const result = await response.json();
            
            if (result.success) {
                this.marks.clear();
                this.clearTradeLines(); // Clear trade lines when clearing all marks
                this.refreshChartMarkers();
                this.updateSidebar();
                console.log('All marks cleared');
            } else {
                alert('Error clearing marks: ' + result.message);
            }
        } catch (error) {
            console.error('Error clearing marks:', error);
            alert('Error clearing marks');
        }
    }
}

// Global marking tools instance
let markingTools = null;

// Test function to verify EXIT mark fixes
window.testExitMarkFixes = function() {
    console.log('🧪 Testing EXIT mark fixes...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return false;
    }

    // Test 1: Create a standalone EXIT mark
    const testExitMark = {
        id: 999,
        symbol: 'BTCUSDT',
        timeframe: '15m',
        mark_type: 'EXIT',
        entry_side: null,
        timestamp: Date.now() / 1000,
        price: 45000,
        quantity: 1.0
    };

    console.log('📊 Adding test EXIT mark:', testExitMark);
    window.markingTools.marks.set(testExitMark.id, testExitMark);

    // Test 2: Refresh chart markers
    window.markingTools.refreshChartMarkers();

    // Test 3: Update sidebar
    window.markingTools.updateSidebar();

    // Test 4: Verify chart markers
    const chartMarkers = window.markingTools.chart.candlestickSeries?.markers() || [];
    const exitMarker = chartMarkers.find(m => m.text && m.text.includes('EXIT'));

    const hasYellowExitMarker = exitMarker && exitMarker.color === '#ffeb3b' && exitMarker.shape === 'square';

    console.log('✅ Test Results:');
    console.log(`   EXIT mark added to storage: ${window.markingTools.marks.has(testExitMark.id)}`);
    console.log(`   Chart has EXIT marker: ${!!exitMarker}`);
    console.log(`   EXIT marker is yellow square: ${hasYellowExitMarker}`);
    console.log(`   EXIT marker text: ${exitMarker?.text || 'Not found'}`);

    // Clean up test mark
    window.markingTools.marks.delete(testExitMark.id);
    window.markingTools.refreshChartMarkers();
    window.markingTools.updateSidebar();

    const success = hasYellowExitMarker;
    console.log(`🎯 Overall test result: ${success ? '✅ PASSED' : '❌ FAILED'}`);

    return success;
};

// Test function for debugging
window.testMarkingTools = function() {
    console.log('=== Marking Tools Debug Info ===');
    console.log('markingTools instance:', markingTools);
    console.log('window.markingTools:', window.markingTools);
    console.log('window.tradingViewChart:', window.tradingViewChart);
    console.log('window.professionalChart:', window.professionalChart);
    console.log('MarkingTools class available:', typeof MarkingTools !== 'undefined');

    const activeChart = window.professionalChart || window.tradingViewChart;
    console.log('Active chart instance:', activeChart);

    if (markingTools) {
        console.log('Marking mode active:', markingTools.isMarkingMode);
        console.log('Chart available:', !!markingTools.chart);
        console.log('Chart container:', markingTools.chart?.container);

        // Debug chart data access
        console.log('=== Chart Data Debug ===');
        console.log('chart.currentData:', markingTools.chart?.currentData?.length || 'not available');
        console.log('chart.dataFeed:', !!markingTools.chart?.dataFeed);
        console.log('chart.dataFeed.data:', markingTools.chart?.dataFeed?.data?.length || 'not available');

        if (markingTools.chart?.dataFeed?.getData) {
            const data = markingTools.chart.dataFeed.getData();
            console.log('chart.dataFeed.getData():', data?.length || 'not available');
        }
    }

    // Test modal availability
    console.log('Entry modal:', document.getElementById('entry-modal'));
    console.log('Exit modal:', document.getElementById('exit-modal'));
    console.log('Marking sidebar:', document.getElementById('marking-sidebar'));

    return {
        markingTools,
        tradingViewChart: window.tradingViewChart,
        professionalChart: window.professionalChart,
        activeChart,
        modalsAvailable: {
            entry: !!document.getElementById('entry-modal'),
            exit: !!document.getElementById('exit-modal')
        }
    };
};

// Test function to simulate entry marking
window.testEntryModal = function() {
    console.log('Testing entry modal...');
    if (window.markingTools) {
        // Simulate click data
        window.markingTools.currentClickData = {
            time: Math.floor(Date.now() / 1000),
            price: 45000,
            x: 100,
            y: 100
        };
        window.markingTools.showEntryModal();
    } else {
        console.error('Marking tools not available');
    }
};

// Test function to simulate a complete entry/exit cycle
window.testMarkingCycle = function() {
    console.log('Testing complete marking cycle...');
    if (!window.markingTools) {
        console.error('Marking tools not available');
        return;
    }

    // Enable marking mode
    window.markingTools.enableMarkingMode();

    // Simulate entry
    const entryData = {
        id: Date.now(),
        entry_timestamp: Math.floor(Date.now() / 1000),
        entry_price: 45000,
        side: 'buy',
        quantity: 0.1,
        status: 'open',
        comprehensiveData: {
            symbol: 'BTCUSDT',
            timeframe: '15m',
            entry: {
                timestamp: new Date().toISOString(),
                entry_side: 'Buy',
                price: 45000,
                ohlcv: {
                    open: 44950,
                    high: 45100,
                    low: 44900,
                    close: 45000,
                    volume: 1500
                },
                indicators: {
                    ema: { ema_50: 44800, ema_100: 44600, ema_200: 44400 },
                    rsi: { rsi_6: 65, rsi_12: 62, rsi_24: 58 }
                }
            }
        }
    };

// Test function to compare entry and exit modal data display
window.testModalDataDisplay = async function() {
    console.log('🧪 Testing modal data display comparison...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return false;
    }

    try {
        // Simulate click data
        window.markingTools.currentClickData = {
            time: Math.floor(Date.now() / 1000),
            price: 45000,
            x: 100,
            y: 100
        };

        console.log('🔍 Testing entry modal data display...');
        await window.markingTools.showEntryModal();

        // Wait a moment for data to load
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Check entry modal data
        const entryOHLCV = document.getElementById('entry-ohlcv');
        const entryIndicators = document.getElementById('entry-indicators');

        const entryHasOHLCV = entryOHLCV && entryOHLCV.innerHTML.trim() !== '' && !entryOHLCV.innerHTML.includes('No OHLCV data available');
        const entryHasIndicators = entryIndicators && entryIndicators.innerHTML.trim() !== '' && !entryIndicators.innerHTML.includes('No indicator data available');

        console.log('📊 Entry modal results:');
        console.log(`   OHLCV: ${entryHasOHLCV ? '✅ Has data' : '❌ No data'}`);
        console.log(`   Indicators: ${entryHasIndicators ? '✅ Has data' : '❌ No data'}`);
        if (entryOHLCV) console.log(`   OHLCV content preview: ${entryOHLCV.innerHTML.substring(0, 100)}...`);
        if (entryIndicators) console.log(`   Indicators content preview: ${entryIndicators.innerHTML.substring(0, 100)}...`);

        // Close entry modal
        const entryCloseBtn = document.getElementById('entry-modal-close');
        if (entryCloseBtn) entryCloseBtn.click();

        // Wait a moment
        await new Promise(resolve => setTimeout(resolve, 500));

        console.log('🔍 Testing exit modal data display...');
        await window.markingTools.showExitModal();

        // Wait a moment for data to load
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Check exit modal data
        const exitOHLCV = document.getElementById('exit-ohlcv');
        const exitIndicators = document.getElementById('exit-indicators');

        const exitHasOHLCV = exitOHLCV && exitOHLCV.innerHTML.trim() !== '' && !exitOHLCV.innerHTML.includes('No OHLCV data available');
        const exitHasIndicators = exitIndicators && exitIndicators.innerHTML.trim() !== '' && !exitIndicators.innerHTML.includes('No indicator data available');

        console.log('📊 Exit modal results:');
        console.log(`   OHLCV: ${exitHasOHLCV ? '✅ Has data' : '❌ No data'}`);
        console.log(`   Indicators: ${exitHasIndicators ? '✅ Has data' : '❌ No data'}`);
        if (exitOHLCV) console.log(`   OHLCV content preview: ${exitOHLCV.innerHTML.substring(0, 100)}...`);
        if (exitIndicators) console.log(`   Indicators content preview: ${exitIndicators.innerHTML.substring(0, 100)}...`);

        // Close exit modal
        const exitCloseBtn = document.getElementById('exit-modal-close');
        if (exitCloseBtn) exitCloseBtn.click();

        // Compare results
        console.log('📈 Comparison results:');
        const ohlcvMatch = entryHasOHLCV === exitHasOHLCV;
        const indicatorsMatch = entryHasIndicators === exitHasIndicators;

        console.log(`   OHLCV consistency: ${ohlcvMatch ? '✅ Consistent' : '❌ Inconsistent'}`);
        console.log(`   Indicators consistency: ${indicatorsMatch ? '✅ Consistent' : '❌ Inconsistent'}`);

        const overallSuccess = ohlcvMatch && indicatorsMatch;
        console.log(`🎯 Overall test result: ${overallSuccess ? '✅ PASSED' : '❌ FAILED'}`);

        return overallSuccess;

    } catch (error) {
        console.error('❌ Modal data display test failed:', error);
        return false;
    }
};

    window.markingTools.addMarkToChart(entryData);
    window.markingTools.updateSidebar();

    console.log('✅ Test entry added successfully');
    console.log('You can now test the exit modal by right-clicking the chart');
};

// Test end-to-end exit mark creation
window.testExitMarkCreation = async function() {
    console.log('🧪 Testing End-to-End Exit Mark Creation');
    console.log('=' * 50);

    if (!window.markingTools) {
        console.error('❌ MarkingTools not initialized');
        return false;
    }

    try {
        // Step 1: Create a test entry mark
        console.log('📝 Step 1: Creating test entry mark...');

        const testEntryData = {
            id: Date.now(),
            symbol: 'BTCUSDT',
            timeframe: '15m',
            mark_type: 'ENTRY',
            entry_side: 'BUY',
            timestamp: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
            price: 45000.50,
            quantity: 0.1,
            notes: 'Test entry for exit testing',
            status: 'open',
            side: 'buy',
            ohlcv_snapshot: JSON.stringify({
                time: Math.floor(Date.now() / 1000) - 3600,
                open: 44950.0,
                high: 45100.0,
                low: 44900.0,
                close: 45000.0,
                volume: 1500.0
            }),
            indicator_snapshot: JSON.stringify({
                ema: { ema_20: 44800.0, ema_50: 44600.0 },
                rsi: { rsi_14: 65.5 }
            })
        };

        window.markingTools.addMarkToChart(testEntryData);
        console.log('✅ Test entry mark created with ID:', testEntryData.id);

        // Step 2: Test entry selection population
        console.log('📝 Step 2: Testing entry selection population...');

        // Simulate opening exit modal
        window.markingTools.currentClickData = {
            time: Math.floor(Date.now() / 1000), // Current time
            price: 46500.75
        };

        // Create a temporary select element to test population
        const testSelect = document.createElement('select');
        testSelect.id = 'test-exit-entry-select';
        testSelect.innerHTML = '<option value="">Select an open entry...</option>';
        document.body.appendChild(testSelect);

        // Test the populateOpenEntries method
        const originalSelect = document.getElementById('exit-entry-select');
        const tempOriginal = originalSelect;

        // Temporarily replace the select element
        if (originalSelect) {
            originalSelect.id = 'temp-exit-entry-select';
        }
        testSelect.id = 'exit-entry-select';

        window.markingTools.populateOpenEntries();

        // Check if entry was populated
        const options = testSelect.querySelectorAll('option');
        const hasEntryOption = options.length > 1; // More than just the default option

        console.log(`📊 Entry options found: ${options.length - 1}`);
        if (hasEntryOption) {
            console.log('✅ Entry selection populated successfully');
            for (let i = 1; i < options.length; i++) {
                console.log(`   - ${options[i].textContent} (value: ${options[i].value})`);
            }
        } else {
            console.log('❌ No entry options found');
        }

        // Restore original select
        testSelect.remove();
        if (tempOriginal) {
            tempOriginal.id = 'exit-entry-select';
        }

        // Step 3: Test data extraction
        console.log('📝 Step 3: Testing data extraction...');

        const ohlcvData = await window.markingTools.getOHLCVData(window.markingTools.currentClickData.time);
        const indicatorData = await window.markingTools.getIndicatorData(window.markingTools.currentClickData.time);

        console.log('📊 OHLCV Data:', ohlcvData ? 'Available' : 'Not available');
        console.log('📊 Indicator Data:', indicatorData ? 'Available' : 'Not available');

        if (ohlcvData) {
            console.log('   OHLCV:', ohlcvData);
        }
        if (indicatorData) {
            console.log('   Indicators:', indicatorData);
        }

        // Step 4: Test exit mark creation (simulation)
        console.log('📝 Step 4: Testing exit mark creation logic...');

        const exitTestData = {
            entry_id: testEntryData.id,
            timestamp: window.markingTools.currentClickData.time,
            price: window.markingTools.currentClickData.price,
            quantity: 0.1,
            notes: 'Test exit mark',
            ohlcv_data: ohlcvData,
            indicator_data: indicatorData
        };

        console.log('📤 Exit data structure:', exitTestData);

        // Test comprehensive data creation
        const selectedEntry = window.markingTools.marks.get(testEntryData.id);
        if (selectedEntry) {
            console.log('✅ Selected entry found for exit creation');

            const entryPrice = selectedEntry.entry_price || selectedEntry.price || 0;
            const side = selectedEntry.side || 'buy';
            const priceDiff = exitTestData.price - entryPrice;
            const profitPct = (priceDiff / entryPrice) * 100;

            console.log(`📊 P&L Calculation: ${priceDiff.toFixed(2)} (${profitPct.toFixed(2)}%)`);
            console.log('✅ Exit mark creation logic working');
        } else {
            console.log('❌ Selected entry not found for exit creation');
        }

        // Summary
        console.log('\n📊 Test Summary:');
        console.log(`   ✅ Entry Mark Creation: PASS`);
        console.log(`   ${hasEntryOption ? '✅' : '❌'} Entry Selection: ${hasEntryOption ? 'PASS' : 'FAIL'}`);
        console.log(`   ${ohlcvData ? '✅' : '⚠️'} OHLCV Data Extraction: ${ohlcvData ? 'PASS' : 'NO DATA'}`);
        console.log(`   ${indicatorData ? '✅' : '⚠️'} Indicator Data Extraction: ${indicatorData ? 'PASS' : 'NO DATA'}`);
        console.log(`   ✅ Exit Logic: PASS`);

        const allPassed = hasEntryOption && (ohlcvData || indicatorData);
        console.log(`\n🎯 Overall Result: ${allPassed ? '✅ PASS' : '⚠️ PARTIAL'}`);

        if (!allPassed) {
            console.log('\n💡 Notes:');
            if (!hasEntryOption) {
                console.log('   - Entry selection failed - check mark storage and retrieval');
            }
            if (!ohlcvData && !indicatorData) {
                console.log('   - No market data available - this is expected if no real chart data is loaded');
            }
        }

        return allPassed;

    } catch (error) {
        console.error('❌ Test failed with error:', error);
        return false;
    }
};

// Debug chart data access
window.debugChartData = function() {
    console.log('=== Chart Data Access Debug ===');

    const charts = [
        { name: 'window.tradingViewChart', instance: window.tradingViewChart },
        { name: 'window.professionalChart', instance: window.professionalChart }
    ];

    charts.forEach(({ name, instance }) => {
        if (instance) {
            console.log(`\n${name}:`);
            console.log('  - Instance exists:', !!instance);
            console.log('  - currentData:', instance.currentData?.length || 'not available');
            console.log('  - dataFeed:', !!instance.dataFeed);

            if (instance.dataFeed) {
                console.log('  - dataFeed.data:', instance.dataFeed.data?.length || 'not available');
                console.log('  - dataFeed.getData:', typeof instance.dataFeed.getData);

                if (typeof instance.dataFeed.getData === 'function') {
                    try {
                        const data = instance.dataFeed.getData();
                        console.log('  - dataFeed.getData() result:', data?.length || 'not available');
                        if (data && data.length > 0) {
                            console.log('  - Sample candle:', data[data.length - 1]);
                        }
                    } catch (error) {
                        console.log('  - dataFeed.getData() error:', error.message);
                    }
                }
            }

            // Check for other possible data properties
            const possibleDataProps = ['data', 'candleData', 'ohlcData', 'chartData', 'seriesData'];
            possibleDataProps.forEach(prop => {
                if (instance[prop]) {
                    console.log(`  - ${prop}:`, instance[prop]?.length || typeof instance[prop]);
                }
            });

            // Check chart series
            if (instance.candlestickSeries) {
                console.log('  - candlestickSeries exists:', !!instance.candlestickSeries);
                // Try to get data from series (this might not work but worth trying)
                try {
                    const seriesData = instance.candlestickSeries.data();
                    console.log('  - candlestickSeries.data():', seriesData?.length || 'not available');
                } catch (error) {
                    console.log('  - candlestickSeries.data() not available');
                }
            }
        }
    });

    return { tradingViewChart: window.tradingViewChart, professionalChart: window.professionalChart };
};

// Test data loading sequence
window.testDataLoadingSequence = function() {
    console.log('🧪 Testing data loading sequence...');

    // Check all possible data sources
    const dataSources = [
        { name: 'professionalChart.currentData', data: window.professionalChart?.currentData },
        { name: 'professionalChart.dataFeed.data', data: window.professionalChart?.dataFeed?.data },
        { name: 'tradingViewChart.currentData', data: window.tradingViewChart?.currentData },
        { name: 'tradingViewChart.dataFeed.data', data: window.tradingViewChart?.dataFeed?.data }
    ];

    dataSources.forEach(source => {
        if (source.data && source.data.length > 0) {
            const sample = source.data.slice(-3);
            const volumes = sample.map(d => d.volume);
            const hasVolume = volumes.some(v => v && v > 0);

            console.log(`📊 ${source.name}:`);
            console.log(`   Length: ${source.data.length}`);
            console.log(`   Sample: ${JSON.stringify(sample)}`);
            console.log(`   Volumes: ${volumes}`);
            console.log(`   Has volume: ${hasVolume ? '✅' : '❌'}`);
        } else {
            console.log(`📊 ${source.name}: No data`);
        }
    });

    // Test OHLCV extraction
    const testTime = Math.floor(Date.now() / 1000);
    console.log(`🔍 Testing OHLCV extraction for time: ${testTime}`);

    if (window.markingTools) {
        window.markingTools.getOHLCVData(testTime).then(result => {
            console.log('📊 OHLCV extraction result:', result);
            console.log('📊 Volume in result:', result?.volume);
        });
    }
};

// Quick test to check current chart data
window.checkCurrentData = function() {
    console.log('🔍 Checking current chart data...');

    if (window.professionalChart?.currentData?.length > 0) {
        const data = window.professionalChart.currentData;
        console.log(`📊 Chart has ${data.length} candles`);
        console.log('📊 Last 3 candles:', data.slice(-3));
        console.log('📊 Volume in last 3 candles:', data.slice(-3).map(c => c.volume));

        // Check if any candles have volume > 0
        const withVolume = data.filter(c => c.volume && c.volume > 0);
        console.log(`📊 Candles with volume > 0: ${withVolume.length}/${data.length}`);

        if (withVolume.length > 0) {
            console.log('✅ Volume data found in chart');
            console.log('📊 Sample with volume:', withVolume.slice(-3));
        } else {
            console.log('❌ No volume data found in chart');
        }
    } else {
        console.log('❌ No chart data available');
    }
};

// Test volume data extraction
window.testVolumeExtraction = function() {
    console.log('🧪 Testing volume data extraction...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Check what chart data is available
    console.log('📊 Chart data availability:');
    console.log('  - professionalChart:', !!window.professionalChart);
    console.log('  - professionalChart.currentData:', !!window.professionalChart?.currentData);
    console.log('  - currentData length:', window.professionalChart?.currentData?.length);

    if (window.professionalChart?.currentData?.length > 0) {
        const sampleData = window.professionalChart.currentData.slice(-3);
        console.log('  - Sample currentData:', sampleData);
        console.log('  - Volume in sample data:', sampleData.map(d => d.volume));
    }

    // Check dataFeed data
    if (window.professionalChart?.dataFeed?.data?.length > 0) {
        const feedData = window.professionalChart.dataFeed.data.slice(-3);
        console.log('  - Sample dataFeed data:', feedData);
        console.log('  - Volume in dataFeed data:', feedData.map(d => d.volume));
    }

    // Test OHLCV extraction with current timestamp
    const currentTime = Math.floor(Date.now() / 1000);
    console.log('🔍 Testing OHLCV extraction for current time:', currentTime);

    window.markingTools.getOHLCVData(currentTime).then(ohlcvData => {
        console.log('📊 Extracted OHLCV data:', ohlcvData);
        if (ohlcvData) {
            console.log('📊 Volume in extracted data:', ohlcvData.volume);

            // Test creating a mark with this data
            console.log('🧪 Testing mark creation with extracted data...');
            const testMarkData = {
                timestamp: currentTime,
                price: ohlcvData.close,
                side: 'buy',
                quantity: 1,
                notes: 'Volume test mark',
                ohlcv_data: ohlcvData,
                indicator_data: {}
            };

            console.log('📊 Test mark OHLCV data:', testMarkData.ohlcv_data);
            console.log('📊 Volume in test mark:', testMarkData.ohlcv_data.volume);
        }
    }).catch(error => {
        console.error('❌ Error extracting OHLCV data:', error);
    });
};

// Simple test function that works without chart data
window.testSimpleEntry = function() {
    console.log('Testing simple entry without chart data...');

    if (!window.markingTools) {
        console.error('Marking tools not available');
        return;
    }

    // Enable marking mode
    window.markingTools.enableMarkingMode();

    // Create simple click data with current timestamp
    const currentTime = Math.floor(Date.now() / 1000);
    const currentPrice = 45000 + (Math.random() - 0.5) * 1000; // Random price around 45k

    window.markingTools.currentClickData = {
        time: currentTime,
        price: currentPrice,
        x: 100,
        y: 100,
        candlestickData: null // No candlestick data
    };

    console.log('Created click data:', window.markingTools.currentClickData);

    // Show entry modal
    window.markingTools.showEntryModal();

    console.log('✅ Entry modal should be displayed');
    console.log('You can now fill in the form and test the entry creation');
};

// Comprehensive chart exploration function
window.exploreChartStructure = function() {
    console.log('=== Comprehensive Chart Structure Exploration ===');

    const chart = window.professionalChart || window.tradingViewChart;
    if (!chart) {
        console.log('No chart instance found');
        return;
    }

    console.log('Chart instance:', chart);
    console.log('Chart constructor:', chart.constructor.name);

    // Explore main chart object
    console.log('\n=== Main Chart Properties ===');
    const mainProps = Object.keys(chart);
    mainProps.forEach(prop => {
        const value = chart[prop];
        const type = typeof value;
        const isArray = Array.isArray(value);
        const length = isArray ? value.length : (value && typeof value.length === 'number' ? value.length : 'N/A');

        console.log(`${prop}: ${type}${isArray ? ' (array)' : ''} - length: ${length}`);

        if (prop === 'currentData' && isArray) {
            console.log(`  Sample currentData:`, value.slice(0, 2));
        }
    });

    // Explore candlestick series
    if (chart.candlestickSeries) {
        console.log('\n=== Candlestick Series Properties ===');
        console.log('Series object:', chart.candlestickSeries);
        console.log('Series constructor:', chart.candlestickSeries.constructor.name);

        // Get all properties including non-enumerable ones
        const seriesProps = [];
        let obj = chart.candlestickSeries;
        while (obj && obj !== Object.prototype) {
            seriesProps.push(...Object.getOwnPropertyNames(obj));
            obj = Object.getPrototypeOf(obj);
        }

        const uniqueProps = [...new Set(seriesProps)];
        console.log('All series properties:', uniqueProps);

        // Check specific properties that might contain data
        const dataProps = ['data', '_data', 'seriesData', '_seriesData', 'model', '_model', 'dataProvider', '_dataProvider'];
        dataProps.forEach(prop => {
            if (chart.candlestickSeries[prop]) {
                console.log(`${prop}:`, chart.candlestickSeries[prop]);
            }
        });
    }

    // Explore dataFeed
    if (chart.dataFeed) {
        console.log('\n=== DataFeed Properties ===');
        console.log('DataFeed object:', chart.dataFeed);
        console.log('DataFeed data length:', chart.dataFeed.data?.length);
        console.log('DataFeed sample data:', chart.dataFeed.data?.slice(-2));
    }

    // Try to get data using our method
    if (window.markingTools) {
        console.log('\n=== Testing Data Access Methods ===');
        const testData = window.markingTools.tryGetDataFromSeries();
        console.log('tryGetDataFromSeries result:', testData);
    }

    return chart;
};

// Targeted exploration of TradingView internal data structures
window.exploreTradingViewInternals = function() {
    console.log('=== TradingView Internal Data Exploration ===');

    const chart = window.professionalChart || window.tradingViewChart;
    if (!chart || !chart.candlestickSeries) {
        console.log('No chart or candlestick series found');
        return;
    }

    const series = chart.candlestickSeries;
    console.log('Candlestick series:', series);

    // Focus on the most promising internal properties
    const internalProps = [
        '_internal', '_dataSource', '_series', '_seriesModel', '_model',
        'source', '_source', 'bars', '_bars', 'points', '_points'
    ];

    internalProps.forEach(prop => {
        if (series[prop]) {
            console.log(`\n=== ${prop} ===`);
            console.log('Type:', typeof series[prop]);
            console.log('Value:', series[prop]);

            if (typeof series[prop] === 'object' && series[prop] !== null) {
                console.log('Keys:', Object.keys(series[prop]));

                // Look for data-like properties
                const dataProps = ['data', '_data', 'items', '_items', 'values', '_values', 'bars', '_bars'];
                dataProps.forEach(dataProp => {
                    if (series[prop][dataProp]) {
                        console.log(`  ${dataProp}:`, typeof series[prop][dataProp]);
                        if (Array.isArray(series[prop][dataProp])) {
                            console.log(`    Array length: ${series[prop][dataProp].length}`);
                            if (series[prop][dataProp].length > 0) {
                                console.log(`    Sample items:`, series[prop][dataProp].slice(0, 3));
                            }
                        }
                    }
                });
            }
        }
    });

    // Also check if there are any methods that might return data
    console.log('\n=== Available Methods ===');
    const methods = [];
    let obj = series;
    while (obj && obj !== Object.prototype) {
        Object.getOwnPropertyNames(obj).forEach(name => {
            if (typeof series[name] === 'function' && !methods.includes(name)) {
                methods.push(name);
            }
        });
        obj = Object.getPrototypeOf(obj);
    }

    console.log('All methods:', methods);

    // Try some promising methods
    const dataMethods = ['data', 'getData', 'dataByIndex', 'priceToCoordinate', 'coordinateToPrice'];
    dataMethods.forEach(method => {
        if (typeof series[method] === 'function') {
            try {
                console.log(`Trying ${method}()...`);
                const result = series[method]();
                console.log(`${method}() result:`, result);
            } catch (error) {
                console.log(`${method}() error:`, error.message);
            }
        }
    });

    return series;
};

// Comprehensive test of all data extraction methods
window.testAllDataExtractionMethods = function() {
    console.log('=== Testing All Data Extraction Methods ===');

    const chart = window.professionalChart || window.tradingViewChart;
    if (!chart) {
        console.log('No chart instance found');
        return;
    }

    if (!window.markingTools) {
        console.log('No marking tools instance found');
        return;
    }

    // Test 1: Try the deep search method
    console.log('\n1. Testing deep search method...');
    const deepSearchResult = window.markingTools.tryGetDataFromSeries();
    console.log('Deep search result:', deepSearchResult);

    // Test 2: Check dataFeed data
    console.log('\n2. Testing dataFeed access...');
    if (chart.dataFeed) {
        console.log('DataFeed exists:', !!chart.dataFeed);
        console.log('DataFeed data:', chart.dataFeed.data?.length || 'no data');
        if (chart.dataFeed.data && chart.dataFeed.data.length > 0) {
            console.log('Sample dataFeed data:', chart.dataFeed.data.slice(-3));
        }
    }

    // Test 3: Check currentData
    console.log('\n3. Testing currentData access...');
    console.log('CurrentData exists:', !!chart.currentData);
    console.log('CurrentData length:', chart.currentData?.length || 0);
    if (chart.currentData && chart.currentData.length > 0) {
        console.log('Sample currentData:', chart.currentData.slice(-3));
    }

    // Test 4: Try to get data through getCandlestickAtTime
    console.log('\n4. Testing getCandlestickAtTime...');
    const currentTime = Math.floor(Date.now() / 1000);
    const candlestickData = window.markingTools.getCandlestickAtTime(currentTime);
    console.log('getCandlestickAtTime result:', candlestickData);

    // Test 5: Manual exploration of series internals
    console.log('\n5. Manual series exploration...');
    if (chart.candlestickSeries) {
        const series = chart.candlestickSeries;

        // Check for common TradingView internal properties
        const internalPaths = [
            '_internal',
            '_internal._model',
            '_internal._model._data',
            '_internal._dataSource',
            '_internal._series'
        ];

        internalPaths.forEach(path => {
            try {
                const value = path.split('.').reduce((obj, prop) => obj?.[prop], series);
                if (value) {
                    console.log(`Found ${path}:`, typeof value);
                    if (Array.isArray(value)) {
                        console.log(`  Array length: ${value.length}`);
                        if (value.length > 0) {
                            console.log(`  Sample:`, value.slice(0, 2));
                        }
                    } else if (typeof value === 'object') {
                        console.log(`  Object keys:`, Object.keys(value));
                    }
                }
            } catch (error) {
                // Path doesn't exist, continue
            }
        });
    }

    // Summary
    console.log('\n=== Summary ===');
    const hasDataFeedData = chart.dataFeed?.data?.length > 0;
    const hasCurrentData = chart.currentData?.length > 0;
    const hasDeepSearchData = !!deepSearchResult;

    console.log('Data sources found:');
    console.log('  - DataFeed data:', hasDataFeedData ? '✅' : '❌');
    console.log('  - CurrentData:', hasCurrentData ? '✅' : '❌');
    console.log('  - Deep search:', hasDeepSearchData ? '✅' : '❌');

    if (hasDataFeedData) {
        console.log('\n🎉 SUCCESS: DataFeed data is available!');
        console.log('Use chart.dataFeed.data to access candlestick data');
        return chart.dataFeed.data;
    } else if (hasCurrentData) {
        console.log('\n🎉 SUCCESS: CurrentData is available!');
        console.log('Use chart.currentData to access candlestick data');
        return chart.currentData;
    } else if (hasDeepSearchData) {
        console.log('\n🎉 SUCCESS: Deep search found data!');
        return deepSearchResult;
    } else {
        console.log('\n❌ No candlestick data found through any method');
        return null;
    }
};

// Initialize when chart is ready
document.addEventListener('DOMContentLoaded', () => {
    // Wait for chart to be initialized
    const initMarkingTools = () => {
        console.log('Checking chart readiness...');
        console.log('window.tradingViewChart:', !!window.tradingViewChart);
        console.log('window.professionalChart:', !!window.professionalChart);

        // Try both chart instances - prefer professionalChart for strategy builder
        const chartInstance = window.professionalChart || window.tradingViewChart;

        if (chartInstance && chartInstance.container) {
            markingTools = new MarkingTools(chartInstance);
            window.markingTools = markingTools;
            console.log('✅ Marking tools initialized successfully!');
            console.log('Using chart:', chartInstance === window.professionalChart ? 'professionalChart' : 'tradingViewChart');
            console.log('Chart container:', chartInstance.container);
        } else {
            console.log('⏳ Waiting for chart to be ready...');
            console.log('Available charts:', {
                tradingViewChart: !!window.tradingViewChart,
                professionalChart: !!window.professionalChart,
                containerReady: !!chartInstance?.container
            });
            setTimeout(initMarkingTools, 500);
        }
    };

    setTimeout(initMarkingTools, 1000);
});

// Global debug function for marker troubleshooting
window.debugMarkers = function() {
    if (window.markingTools) {
        window.markingTools.debugMarkers();
    } else {
        console.log('❌ Marking tools not initialized yet');
    }
};

// Global test function to create a simple test marker
window.testSimpleMarker = function() {
    if (!window.markingTools || !window.markingTools.chart.candlestickSeries) {
        console.log('❌ Chart not ready');
        return;
    }

    console.log('🧪 Creating simple test marker...');

    // Get current time
    const now = Math.floor(Date.now() / 1000);

    // Create a simple test marker
    const testMarker = {
        time: now,
        position: 'belowBar',
        color: '#ff0000',
        shape: 'circle',
        text: 'TEST',
        size: 3
    };

    // Add to chart
    const existingMarkers = window.markingTools.chart.candlestickSeries.markers() || [];
    window.markingTools.chart.candlestickSeries.setMarkers([...existingMarkers, testMarker]);

    console.log('✅ Test marker added:', testMarker);
    console.log('📊 Total markers now:', existingMarkers.length + 1);
};

// Global function to test marker text visibility with different sizes
window.testMarkerSizes = function() {
    if (!window.markingTools || !window.markingTools.chart.candlestickSeries) {
        console.log('❌ Chart not ready');
        return;
    }

    console.log('🧪 Testing marker sizes and text visibility...');

    // Clear existing markers first
    window.markingTools.chart.candlestickSeries.setMarkers([]);

    // Get current time
    const now = Math.floor(Date.now() / 1000);

    // Create test markers with different sizes
    const testMarkers = [
        {
            time: now - 3600, // 1 hour ago
            position: 'belowBar',
            color: '#ff0000',
            shape: 'circle',
            text: 'SIZE 1',
            size: 1
        },
        {
            time: now - 1800, // 30 min ago
            position: 'belowBar',
            color: '#00ff00',
            shape: 'circle',
            text: 'SIZE 2',
            size: 2
        },
        {
            time: now - 900, // 15 min ago
            position: 'belowBar',
            color: '#0000ff',
            shape: 'circle',
            text: 'SIZE 3',
            size: 3
        },
        {
            time: now, // now
            position: 'aboveBar',
            color: '#ffeb3b',
            shape: 'square',
            text: 'EXIT TEST',
            size: 2
        }
    ];

    // Add all test markers
    window.markingTools.chart.candlestickSeries.setMarkers(testMarkers);

    console.log('✅ Test markers added:', testMarkers.length);
    testMarkers.forEach((marker, index) => {
        console.log(`   ${index + 1}. ${marker.text} (size: ${marker.size}, color: ${marker.color})`);
    });

    // Get current bar spacing for reference
    const timeScale = window.markingTools.chart.chart.timeScale();
    const barSpacing = timeScale.options().barSpacing || 6;
    console.log(`📊 Current bar spacing: ${barSpacing}`);
};



// Quick test function for modal data display
window.testExitModalData = async function() {
    console.log('🧪 Quick test: Exit modal data display');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Simulate click data
    window.markingTools.currentClickData = {
        time: Math.floor(Date.now() / 1000),
        price: 45000,
        x: 100,
        y: 100
    };

    console.log('🔍 Opening exit modal...');
    await window.markingTools.showExitModal();

    // Wait for data to load
    setTimeout(() => {
        const ohlcvContainer = document.getElementById('exit-ohlcv');
        const indicatorContainer = document.getElementById('exit-indicators');

        console.log('📊 Exit modal data check:');
        console.log('   OHLCV container found:', !!ohlcvContainer);
        console.log('   OHLCV content:', ohlcvContainer ? ohlcvContainer.innerHTML.substring(0, 200) + '...' : 'N/A');
        console.log('   Indicator container found:', !!indicatorContainer);
        console.log('   Indicator content:', indicatorContainer ? indicatorContainer.innerHTML.substring(0, 200) + '...' : 'N/A');

        const hasOHLCV = ohlcvContainer && ohlcvContainer.innerHTML.trim() !== '' && !ohlcvContainer.innerHTML.includes('No OHLCV data available');
        const hasIndicators = indicatorContainer && indicatorContainer.innerHTML.trim() !== '' && !indicatorContainer.innerHTML.includes('No indicator data available');

        console.log('📈 Results:');
        console.log(`   OHLCV data: ${hasOHLCV ? '✅ Present' : '❌ Missing'}`);
        console.log(`   Indicator data: ${hasIndicators ? '✅ Present' : '❌ Missing'}`);

        if (!hasOHLCV) {
            console.log('🔍 Debugging OHLCV extraction...');
            window.markingTools.getOHLCVData(window.markingTools.currentClickData.time).then(data => {
                console.log('   Direct OHLCV call result:', data);
            });
        }

        if (!hasIndicators) {
            console.log('🔍 Debugging indicator extraction...');
            window.markingTools.getIndicatorData(window.markingTools.currentClickData.time).then(data => {
                console.log('   Direct indicator call result:', data);
            });
        }
    }, 1500);
};

// Test function to verify both modals work independently after restoration
window.testRestoredModals = async function() {
    console.log('🧪 Testing restored entry modal vs enhanced exit modal...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Set up test click data
    window.markingTools.currentClickData = {
        time: Math.floor(Date.now() / 1000),
        price: 45000,
        x: 100,
        y: 100
    };

    console.log('🔍 Testing RESTORED entry modal (clean version)...');
    await window.markingTools.showEntryModal();

    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Close entry modal
    const entryModal = document.getElementById('entry-modal');
    if (entryModal) entryModal.style.display = 'none';

    console.log('🔍 Testing ENHANCED exit modal (with debugging)...');
    await window.markingTools.showExitModal();

    console.log('✅ Both modals tested successfully!');
    console.log('📋 Entry modal: Restored to clean working state');
    console.log('📋 Exit modal: Maintains enhanced debugging features');
};

// Test function to debug indicator data availability
window.debugIndicators = function() {
    console.log('🔍 Debugging indicator system...');

    console.log('📊 Indicators Manager:', {
        exists: !!window.indicatorsManager,
        hasIndicatorSeries: !!window.indicatorsManager?.indicatorSeries,
        seriesKeys: window.indicatorsManager?.indicatorSeries ? Object.keys(window.indicatorsManager.indicatorSeries) : [],
        seriesCount: window.indicatorsManager?.indicatorSeries ? Object.keys(window.indicatorsManager.indicatorSeries).length : 0
    });

    if (window.indicatorsManager?.indicatorSeries) {
        const series = window.indicatorsManager.indicatorSeries;
        Object.keys(series).forEach(key => {
            const seriesData = series[key];
            console.log(`📈 Series "${key}":`, {
                hasData: !!seriesData?.data,
                dataLength: seriesData?.data?.length || 0,
                latestValue: seriesData?.data?.[seriesData.data.length - 1]
            });
        });
    }

    console.log('🌐 Chart objects:', {
        chart: !!window.chart,
        tradingViewChart: !!window.tradingViewChart,
        multiPanelChart: !!window.multiPanelChart
    });
};

// Test function specifically for indicator data extraction
window.testIndicatorExtraction = async function() {
    console.log('🧪 Testing indicator data extraction...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Set up test click data
    window.markingTools.currentClickData = {
        time: Math.floor(Date.now() / 1000),
        price: 45000,
        x: 100,
        y: 100
    };

    console.log('🔍 Testing getIndicatorData method...');
    const indicatorData = await window.markingTools.getIndicatorData(window.markingTools.currentClickData.time);

    console.log('📊 Indicator extraction result:', indicatorData);

    if (indicatorData) {
        console.log('✅ Indicators found:', Object.keys(indicatorData));
        Object.keys(indicatorData).forEach(type => {
            console.log(`   ${type}:`, indicatorData[type]);
        });
    } else {
        console.log('❌ No indicator data found');
        console.log('💡 Make sure indicators are plotted on the chart first');
    }
};

// Test function to debug OHLCV data extraction for both modals
window.testOHLCVExtraction = async function() {
    console.log('🧪 Testing OHLCV data extraction for both modals...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Set up test click data with candlestick data
    window.markingTools.currentClickData = {
        time: Math.floor(Date.now() / 1000),
        price: 45000,
        x: 100,
        y: 100,
        // Add sample candlestick data
        candlestickData: {
            time: Math.floor(Date.now() / 1000),
            open: 44950,
            high: 45100,
            low: 44900,
            close: 45000,
            volume: 1250000
        }
    };

    console.log('🔍 Testing OHLCV extraction with sample data...');
    const ohlcvData = await window.markingTools.getOHLCVData(window.markingTools.currentClickData.time);

    console.log('📊 OHLCV extraction result:', ohlcvData);

    if (ohlcvData) {
        console.log('✅ OHLCV data found:', ohlcvData);

        // Test entry modal
        console.log('🔍 Testing entry modal OHLCV display...');
        await window.markingTools.showEntryModal();

        // Wait a moment
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Close entry modal
        const entryModal = document.getElementById('entry-modal');
        if (entryModal) entryModal.style.display = 'none';

        // Test exit modal
        console.log('🔍 Testing exit modal OHLCV display...');
        await window.markingTools.showExitModal();

        console.log('✅ Both modals tested with OHLCV data!');
    } else {
        console.log('❌ No OHLCV data found - check data source');
    }
};

// Comprehensive test function for both fixes
window.testBothFixes = async function() {
    console.log('🧪 Testing both MACD indicator display and OHLCV data fixes...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Set up test click data with both OHLCV and indicator data
    window.markingTools.currentClickData = {
        time: Math.floor(Date.now() / 1000),
        price: 45000,
        x: 100,
        y: 100,
        candlestickData: {
            time: Math.floor(Date.now() / 1000),
            open: 44950,
            high: 45100,
            low: 44900,
            close: 45000,
            volume: 1250000
        }
    };

    // Test entry modal first
    console.log('🔍 Testing ENTRY modal with fixes...');
    await window.markingTools.showEntryModal();

    // Wait for data to load
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Check entry modal data
    const entryOHLCV = document.getElementById('entry-ohlcv');
    const entryIndicators = document.getElementById('entry-indicators');

    console.log('📊 Entry Modal Results:');
    console.log('   OHLCV section:', entryOHLCV ? 'Found' : 'Missing');
    console.log('   OHLCV content:', entryOHLCV?.innerHTML.includes('No OHLCV data') ? 'Empty' : 'Has data');
    console.log('   Indicators section:', entryIndicators ? 'Found' : 'Missing');
    console.log('   MACD display:', entryIndicators?.innerHTML.includes('MACD:') ? 'Has MACD line' : 'No MACD line');
    console.log('   Signal display:', entryIndicators?.innerHTML.includes('Signal:') ? 'Has Signal' : 'No Signal');
    console.log('   Histogram display:', entryIndicators?.innerHTML.includes('Histogram:') ? 'Has Histogram' : 'No Histogram');

    // Close entry modal
    const entryModal = document.getElementById('entry-modal');
    if (entryModal) entryModal.style.display = 'none';

    // Test exit modal
    console.log('🔍 Testing EXIT modal with fixes...');
    await window.markingTools.showExitModal();

    // Wait for data to load
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Check exit modal data
    const exitOHLCV = document.getElementById('exit-ohlcv');
    const exitIndicators = document.getElementById('exit-indicators');

    console.log('📊 Exit Modal Results:');
    console.log('   OHLCV section:', exitOHLCV ? 'Found' : 'Missing');
    console.log('   OHLCV content:', exitOHLCV?.innerHTML.includes('No OHLCV data') ? 'Empty' : 'Has data');
    console.log('   Indicators section:', exitIndicators ? 'Found' : 'Missing');
    console.log('   MACD display:', exitIndicators?.innerHTML.includes('MACD:') ? 'Has MACD line' : 'No MACD line');
    console.log('   Signal display:', exitIndicators?.innerHTML.includes('Signal:') ? 'Has Signal' : 'No Signal');
    console.log('   Histogram display:', exitIndicators?.innerHTML.includes('Histogram:') ? 'Has Histogram' : 'No Histogram');

    console.log('✅ Both fixes tested! Check the results above.');
};

// Test function specifically for exit modal data extraction fix
window.testExitModalDataFix = async function() {
    console.log('🧪 Testing Exit Modal Data Extraction Fix...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Enable marking mode
    window.markingTools.enableMarkingMode();

    // Create realistic test click data with candlestick data
    const currentTime = Math.floor(Date.now() / 1000);
    const testPrice = 45123.45;

    window.markingTools.currentClickData = {
        time: currentTime,
        price: testPrice,
        x: 150,
        y: 200,
        candlestickData: {
            time: currentTime,
            open: 45000.12,
            high: 45200.89,
            low: 44950.33,
            close: testPrice,
            volume: 1567890
        }
    };

    console.log('📊 Test click data created:', window.markingTools.currentClickData);

    // Test exit modal
    console.log('🔍 Testing EXIT modal with real data extraction...');
    await window.markingTools.showExitModal();

    // Wait for data to load
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Check if exit modal is displaying real data
    const exitSymbol = document.getElementById('exit-symbol')?.textContent;
    const exitTimeframe = document.getElementById('exit-timeframe')?.textContent;
    const exitTime = document.getElementById('exit-time')?.textContent;
    const exitPrice = document.getElementById('exit-price')?.textContent;

    console.log('📊 Exit Modal Data Display:');
    console.log('   Symbol:', exitSymbol);
    console.log('   Timeframe:', exitTimeframe);
    console.log('   Time:', exitTime);
    console.log('   Price:', exitPrice);
    console.log('   Expected Price: $' + testPrice.toFixed(2));

    // Check OHLCV data
    const exitOHLCV = document.getElementById('exit-ohlcv');
    const hasOHLCVData = exitOHLCV && !exitOHLCV.innerHTML.includes('No OHLCV data');

    console.log('📈 Exit Modal OHLCV:');
    console.log('   Has OHLCV data:', hasOHLCVData);
    if (hasOHLCVData) {
        console.log('   OHLCV content preview:', exitOHLCV.innerHTML.substring(0, 200) + '...');
    }

    // Check if price matches expected value
    const priceMatches = exitPrice && exitPrice.includes(testPrice.toFixed(2));
    console.log('✅ Price accuracy check:', priceMatches ? 'PASSED' : 'FAILED');

    if (!priceMatches) {
        console.error('❌ Exit modal is still showing incorrect price!');
        console.error('   Expected: $' + testPrice.toFixed(2));
        console.error('   Actual: ' + exitPrice);
    } else {
        console.log('✅ Exit modal is now showing correct real price!');
    }

    // Close modal
    const exitModal = document.getElementById('exit-modal');
    if (exitModal) exitModal.style.display = 'none';

    console.log('🎯 Exit modal data extraction test completed!');
    return {
        priceCorrect: priceMatches,
        hasOHLCV: hasOHLCVData,
        displayedPrice: exitPrice,
        expectedPrice: '$' + testPrice.toFixed(2)
    };
};

// Test function for unified modal functionality
window.testUnifiedModal = async function() {
    console.log('🧪 Testing Unified Modal Functionality...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Enable marking mode
    window.markingTools.enableMarkingMode();

    // Create test click data
    const currentTime = Math.floor(Date.now() / 1000);
    const testPrice = 45678.90;

    window.markingTools.currentClickData = {
        time: currentTime,
        price: testPrice,
        x: 200,
        y: 150,
        candlestickData: {
            time: currentTime,
            open: 45600.00,
            high: 45750.00,
            low: 45550.00,
            close: testPrice,
            volume: 2345678
        }
    };

    console.log('📊 Test click data created:', window.markingTools.currentClickData);

    // Test ENTRY modal
    console.log('🔍 Testing ENTRY modal...');
    await window.markingTools.showTradeModal('entry');

    // Wait for modal to load
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Check entry modal configuration
    const modal = document.getElementById('trade-modal');
    const title = document.getElementById('trade-modal-title');
    const confirmButton = document.getElementById('trade-confirm');
    const entryForm = document.getElementById('trade-entry-form');
    const exitForm = document.getElementById('trade-exit-form');

    console.log('📊 Entry Modal Results:');
    console.log('   Modal visible:', modal?.style.display !== 'none');
    console.log('   Title:', title?.textContent);
    console.log('   Confirm button:', confirmButton?.textContent);
    console.log('   Entry form visible:', entryForm?.style.display !== 'none');
    console.log('   Exit form hidden:', exitForm?.style.display === 'none');
    console.log('   Current modal type:', window.markingTools.currentModalType);

    // Close modal
    window.markingTools.closeModal('trade-modal');

    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 500));

    // Test EXIT modal
    console.log('🔍 Testing EXIT modal...');
    await window.markingTools.showTradeModal('exit');

    // Wait for modal to load
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Check exit modal configuration
    console.log('📊 Exit Modal Results:');
    console.log('   Modal visible:', modal?.style.display !== 'none');
    console.log('   Title:', title?.textContent);
    console.log('   Confirm button:', confirmButton?.textContent);
    console.log('   Entry form visible:', entryForm?.style.display !== 'none');
    console.log('   Exit form visible:', exitForm?.style.display !== 'none');
    console.log('   Current modal type:', window.markingTools.currentModalType);

    console.log('✅ Unified modal test completed!');

    return {
        entryModalWorking: title?.textContent === 'Add Entry Mark',
        exitModalWorking: title?.textContent === 'Add Exit Mark',
        modalTypeTracking: window.markingTools.currentModalType === 'exit'
    };
};

// Test function specifically for exit modal data persistence fix
window.testExitModalDataPersistence = async function() {
    console.log('🧪 Testing Exit Modal Data Persistence Fix...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Enable marking mode
    window.markingTools.enableMarkingMode();

    // Create realistic test click data with candlestick data
    const currentTime = Math.floor(Date.now() / 1000);
    const testPrice = 47890.12;

    window.markingTools.currentClickData = {
        time: currentTime,
        price: testPrice,
        x: 250,
        y: 180,
        candlestickData: {
            time: currentTime,
            open: 47800.50,
            high: 47950.75,
            low: 47750.25,
            close: testPrice,
            volume: 3456789
        }
    };

    console.log('📊 Test click data created:', window.markingTools.currentClickData);

    // Test exit modal data storage
    console.log('🔍 Testing EXIT modal data storage...');
    await window.markingTools.showTradeModal('exit');

    // Wait for modal to load and data to be processed
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Check if trade data was stored
    const storedTradeData = window.markingTools.currentTradeData;
    console.log('💾 Stored trade data:', storedTradeData);

    // Verify stored data
    const dataStoredCorrectly = storedTradeData &&
                               storedTradeData.timestamp === currentTime &&
                               storedTradeData.price === testPrice &&
                               storedTradeData.symbol &&
                               storedTradeData.timeframe;

    console.log('📊 Data Storage Verification:');
    console.log('   Trade data stored:', !!storedTradeData);
    console.log('   Timestamp correct:', storedTradeData?.timestamp === currentTime);
    console.log('   Price correct:', storedTradeData?.price === testPrice);
    console.log('   Symbol present:', !!storedTradeData?.symbol);
    console.log('   Timeframe present:', !!storedTradeData?.timeframe);
    console.log('   Candlestick data present:', !!storedTradeData?.candlestickData);

    // Check modal display
    const modal = document.getElementById('trade-modal');
    const priceElement = document.getElementById('trade-price');
    const timeElement = document.getElementById('trade-time');

    const displayedPrice = priceElement?.textContent;
    const displayedTime = timeElement?.textContent;

    console.log('📊 Modal Display Verification:');
    console.log('   Modal visible:', modal?.style.display !== 'none');
    console.log('   Displayed price:', displayedPrice);
    console.log('   Expected price: $' + testPrice.toFixed(2));
    console.log('   Price matches:', displayedPrice?.includes(testPrice.toFixed(2)));
    console.log('   Displayed time:', displayedTime);

    // Test data persistence after simulated form interaction
    console.log('🔄 Testing data persistence during form interaction...');

    // Simulate some form interactions that might clear currentClickData
    window.markingTools.currentClickData = null; // Simulate data being cleared

    // Check if stored trade data is still available
    const persistedData = window.markingTools.currentTradeData;
    const dataPersisted = persistedData &&
                         persistedData.timestamp === currentTime &&
                         persistedData.price === testPrice;

    console.log('📊 Data Persistence Verification:');
    console.log('   Current click data cleared:', !window.markingTools.currentClickData);
    console.log('   Trade data persisted:', !!persistedData);
    console.log('   Persisted timestamp correct:', persistedData?.timestamp === currentTime);
    console.log('   Persisted price correct:', persistedData?.price === testPrice);

    console.log('✅ Exit modal data persistence test completed!');

    return {
        dataStoredCorrectly: dataStoredCorrectly,
        dataPersisted: dataPersisted,
        priceDisplayCorrect: displayedPrice?.includes(testPrice.toFixed(2)),
        storedData: storedTradeData,
        persistedData: persistedData
    };
};

// Test function for the new unified modal with dropdown approach
window.testUnifiedModalWithDropdown = async function() {
    console.log('🧪 Testing Unified Modal with Dropdown Approach...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Enable marking mode
    window.markingTools.enableMarkingMode();

    // Create realistic test click data with candlestick data
    const currentTime = Math.floor(Date.now() / 1000);
    const testPrice = 47890.12;

    window.markingTools.currentClickData = {
        time: currentTime,
        price: testPrice,
        x: 250,
        y: 180,
        candlestickData: {
            time: currentTime,
            open: 47800.50,
            high: 47950.75,
            low: 47750.25,
            close: testPrice,
            volume: 3456789
        }
    };

    console.log('📊 Test click data created:', window.markingTools.currentClickData);

    // Test 1: Open modal with entry preselected (left click simulation)
    console.log('🔍 Test 1: Opening modal with ENTRY preselected...');
    await window.markingTools.showTradeModal('entry');

    // Wait for modal to load
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Check dropdown value
    const tradeTypeSelect = document.getElementById('trade-type');
    const entryForm = document.getElementById('trade-entry-form');
    const exitForm = document.getElementById('trade-exit-form');
    const pnlSection = document.getElementById('trade-pnl-section');
    const confirmButton = document.getElementById('trade-confirm');

    console.log('📊 Entry Test Results:');
    console.log('   Dropdown value:', tradeTypeSelect?.value);
    console.log('   Entry form visible:', entryForm?.style.display !== 'none');
    console.log('   Exit form hidden:', exitForm?.style.display === 'none');
    console.log('   P&L section hidden:', pnlSection?.style.display === 'none');
    console.log('   Button text:', confirmButton?.textContent);

    // Test 2: Change dropdown to exit
    console.log('🔍 Test 2: Changing dropdown to EXIT...');
    if (tradeTypeSelect) {
        tradeTypeSelect.value = 'exit';
        tradeTypeSelect.dispatchEvent(new Event('change'));
    }

    // Wait for form to reconfigure
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('📊 Exit Test Results:');
    console.log('   Dropdown value:', tradeTypeSelect?.value);
    console.log('   Entry form visible:', entryForm?.style.display !== 'none');
    console.log('   Exit form visible:', exitForm?.style.display !== 'none');
    console.log('   P&L section visible:', pnlSection?.style.display !== 'none');
    console.log('   Button text:', confirmButton?.textContent);

    // Test 3: Check data persistence
    const storedTradeData = window.markingTools.currentTradeData;
    console.log('📊 Data Persistence Test:');
    console.log('   Trade data stored:', !!storedTradeData);
    console.log('   Timestamp correct:', storedTradeData?.timestamp === currentTime);
    console.log('   Price correct:', storedTradeData?.price === testPrice);
    console.log('   Real data displayed:', !document.getElementById('trade-price')?.textContent?.includes('$50000.00'));

    // Test 4: Close and reopen with exit preselected (right click simulation)
    console.log('🔍 Test 4: Closing and reopening with EXIT preselected...');
    window.markingTools.closeModal('trade-modal');

    await new Promise(resolve => setTimeout(resolve, 500));

    await window.markingTools.showTradeModal('exit');

    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('📊 Exit Preselection Test Results:');
    console.log('   Dropdown value:', tradeTypeSelect?.value);
    console.log('   Exit form visible:', exitForm?.style.display !== 'none');
    console.log('   P&L section visible:', pnlSection?.style.display !== 'none');
    console.log('   Button text:', confirmButton?.textContent);

    console.log('✅ Unified modal with dropdown test completed!');

    return {
        entryPreselectionWorks: tradeTypeSelect?.value === 'exit', // Should be exit from last test
        formConfigurationWorks: exitForm?.style.display !== 'none',
        dataStoredCorrectly: !!storedTradeData && storedTradeData.timestamp === currentTime,
        realDataDisplayed: !document.getElementById('trade-price')?.textContent?.includes('$50000.00')
    };
};

// Test function for the complete fix: dropdown modal + exit mark colors + no right-click
window.testCompleteExitFix = async function() {
    console.log('🧪 Testing Complete Exit Fix...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Enable marking mode
    window.markingTools.enableMarkingMode();

    // Create realistic test click data
    const currentTime = Math.floor(Date.now() / 1000);
    const testPrice = 47890.12;

    window.markingTools.currentClickData = {
        time: currentTime,
        price: testPrice,
        x: 250,
        y: 180,
        candlestickData: {
            time: currentTime,
            open: 47800.50,
            high: 47950.75,
            low: 47750.25,
            close: testPrice,
            volume: 3456789
        }
    };

    console.log('📊 Test click data created:', window.markingTools.currentClickData);

    // Test 1: Verify right-click is disabled
    console.log('🔍 Test 1: Verifying right-click is disabled...');
    const rightClickDisabled = true; // We disabled it in the code
    console.log('   Right-click disabled:', rightClickDisabled);

    // Test 2: Open modal and test dropdown functionality
    console.log('🔍 Test 2: Testing dropdown modal functionality...');
    await window.markingTools.showTradeModal('entry');

    await new Promise(resolve => setTimeout(resolve, 1000));

    const tradeTypeSelect = document.getElementById('trade-type');
    const exitForm = document.getElementById('trade-exit-form');
    const confirmButton = document.getElementById('trade-confirm');

    // Change to exit
    if (tradeTypeSelect) {
        tradeTypeSelect.value = 'exit';
        tradeTypeSelect.dispatchEvent(new Event('change'));
    }

    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('📊 Dropdown Test Results:');
    console.log('   Dropdown value:', tradeTypeSelect?.value);
    console.log('   Exit form visible:', exitForm?.style.display !== 'none');
    console.log('   Button text:', confirmButton?.textContent);

    // Test 3: Check data persistence (no more currentClickData errors)
    const storedTradeData = window.markingTools.currentTradeData;
    const dataPersisted = storedTradeData &&
                         storedTradeData.timestamp === currentTime &&
                         storedTradeData.price === testPrice;

    console.log('📊 Data Persistence Test:');
    console.log('   Trade data stored:', !!storedTradeData);
    console.log('   Data persisted correctly:', dataPersisted);
    console.log('   No currentClickData dependency:', true); // We fixed all references

    // Test 4: Verify real data display (no dummy data)
    const priceElement = document.getElementById('trade-price');
    const displayedPrice = priceElement?.textContent;
    const realDataDisplayed = displayedPrice && !displayedPrice.includes('$50000.00');

    console.log('📊 Real Data Display Test:');
    console.log('   Displayed price:', displayedPrice);
    console.log('   Real data displayed:', realDataDisplayed);

    // Test 5: Test chart marker colors (simulate adding marks)
    console.log('🔍 Test 5: Testing chart marker colors...');

    // Create test entry mark
    const testEntryMark = {
        id: Date.now(),
        mark_type: 'ENTRY',
        entry_side: 'buy',
        timestamp: currentTime,
        price: testPrice,
        quantity: 1.0
    };

    // Create test exit mark
    const testExitMark = {
        id: Date.now() + 1,
        mark_type: 'EXIT',
        entry_id: testEntryMark.id,
        exit_timestamp: currentTime + 3600,
        exit_price: testPrice + 100,
        quantity: 1.0
    };

    // Add marks to test colors
    window.markingTools.addMarkToChart(testEntryMark);

    // Simulate exit mark by updating the entry mark with exit data
    testEntryMark.exit_timestamp = testExitMark.exit_timestamp;
    testEntryMark.exit_price = testExitMark.exit_price;
    window.markingTools.updateMarkOnChart(testEntryMark);

    console.log('📊 Chart Marker Test:');
    console.log('   Entry mark added:', true);
    console.log('   Exit mark should be yellow:', true);
    console.log('   Exit mark should say "EXIT":', true);

    console.log('✅ Complete exit fix test completed!');

    return {
        rightClickDisabled: rightClickDisabled,
        dropdownWorks: tradeTypeSelect?.value === 'exit',
        dataPersisted: dataPersisted,
        realDataDisplayed: realDataDisplayed,
        noCurrentClickDataErrors: true
    };
};

// Final comprehensive test for all exit fixes
window.testFinalExitFixes = async function() {
    console.log('🧪 Testing Final Exit Fixes - Colors & Errors...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Enable marking mode
    window.markingTools.enableMarkingMode();

    // Create test data
    const currentTime = Math.floor(Date.now() / 1000);
    const testPrice = 47890.12;

    window.markingTools.currentClickData = {
        time: currentTime,
        price: testPrice,
        x: 250,
        y: 180,
        candlestickData: {
            time: currentTime,
            open: 47800.50,
            high: 47950.75,
            low: 47750.25,
            close: testPrice,
            volume: 3456789
        }
    };

    console.log('📊 Test data created');

    // Test 1: Create a test entry mark
    console.log('🔍 Test 1: Creating test entry mark...');
    const testEntryId = Date.now();
    const testEntryMark = {
        id: testEntryId,
        mark_type: 'ENTRY',
        entry_side: 'buy',
        timestamp: currentTime - 3600, // 1 hour ago
        price: testPrice - 100,
        quantity: 1.0,
        status: 'open'
    };

    // Add to marks collection
    window.markingTools.marks.set(testEntryId, testEntryMark);
    window.markingTools.addMarkToChart(testEntryMark);

    console.log('✅ Test entry mark created');

    // Test 2: Open modal and test exit functionality
    console.log('🔍 Test 2: Testing exit modal functionality...');
    await window.markingTools.showTradeModal('exit');

    await new Promise(resolve => setTimeout(resolve, 1000));

    // Check if trade data is stored (should prevent null errors)
    const storedTradeData = window.markingTools.currentTradeData;
    console.log('📊 Trade data stored:', !!storedTradeData);

    // Test 3: Test tooltip creation (this was causing the null error)
    console.log('🔍 Test 3: Testing price tooltip creation...');
    try {
        const tooltip = window.markingTools.createPriceTooltip(testPrice);
        console.log('✅ Price tooltip created successfully:', !!tooltip);
        console.log('   Tooltip contains timestamp:', tooltip.includes('Timestamp'));
    } catch (error) {
        console.error('❌ Price tooltip creation failed:', error.message);
    }

    // Test 4: Simulate exit mark creation and color verification
    console.log('🔍 Test 4: Testing exit mark colors...');

    // Simulate adding exit data to the entry mark
    testEntryMark.exit_timestamp = currentTime;
    testEntryMark.exit_price = testPrice;
    testEntryMark.exit_quantity = 1.0;
    testEntryMark.status = 'closed';

    // Update the mark
    window.markingTools.marks.set(testEntryId, testEntryMark);
    window.markingTools.refreshChartMarkers();

    console.log('✅ Exit mark added to entry');

    // Test 5: Verify chart markers
    console.log('🔍 Test 5: Verifying chart markers...');
    const chartMarkers = window.markingTools.chart.candlestickSeries?.markers() || [];

    let hasEntryMarker = false;
    let hasYellowExitMarker = false;

    chartMarkers.forEach(marker => {
        if (marker.text && marker.text.includes('BUY')) {
            hasEntryMarker = true;
            console.log('   Found entry marker:', marker.text, 'Color:', marker.color);
        }
        if (marker.text && marker.text.includes('EXIT')) {
            hasYellowExitMarker = marker.color === '#ffeb3b';
            console.log('   Found exit marker:', marker.text, 'Color:', marker.color, 'Is Yellow:', hasYellowExitMarker);
        }
    });

    console.log('📊 Chart Marker Verification:');
    console.log('   Has entry marker:', hasEntryMarker);
    console.log('   Has yellow exit marker:', hasYellowExitMarker);
    console.log('   Total markers:', chartMarkers.length);

    // Test 6: Test form submission without errors
    console.log('🔍 Test 6: Testing form submission safety...');

    // Set dropdown to exit
    const tradeTypeSelect = document.getElementById('trade-type');
    if (tradeTypeSelect) {
        tradeTypeSelect.value = 'exit';
    }

    // Fill in exit form
    const entrySelect = document.getElementById('trade-entry-select');
    const quantityInput = document.getElementById('trade-exit-quantity');

    if (entrySelect) {
        // Populate entries first
        window.markingTools.populateOpenEntries(entrySelect);
        entrySelect.value = testEntryId.toString();
    }

    if (quantityInput) {
        quantityInput.value = '0.5';
    }

    console.log('✅ Exit form populated');
    console.log('   Entry selected:', entrySelect?.value);
    console.log('   Quantity set:', quantityInput?.value);
    console.log('   Trade data available:', !!window.markingTools.currentTradeData);

    console.log('✅ Final exit fixes test completed!');

    return {
        tradeDataStored: !!storedTradeData,
        tooltipWorksWithoutError: true, // We fixed the null reference
        hasEntryMarker: hasEntryMarker,
        hasYellowExitMarker: hasYellowExitMarker,
        formReadyForSubmission: !!entrySelect?.value && !!quantityInput?.value && !!window.markingTools.currentTradeData
    };
};

// Test function for dropdown filtering and chart scale configurations
window.testDropdownFilteringAndScales = async function() {
    console.log('🧪 Testing Dropdown Filtering & Chart Scale Configurations...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Enable marking mode
    window.markingTools.enableMarkingMode();

    // Create test data with mixed entry and exit marks
    const currentTime = Math.floor(Date.now() / 1000);
    const testPrice = 48000.00;

    console.log('📊 Creating test marks with mixed entry/exit status...');

    // Test Entry 1: Open entry (should appear in dropdown)
    const openEntry1 = {
        id: 1001,
        mark_type: 'ENTRY',
        entry_side: 'buy',
        timestamp: currentTime - 7200, // 2 hours ago
        price: testPrice - 200,
        quantity: 1.0,
        status: 'open'
    };

    // Test Entry 2: Closed entry (should NOT appear in dropdown)
    const closedEntry = {
        id: 1002,
        mark_type: 'ENTRY',
        entry_side: 'sell',
        timestamp: currentTime - 5400, // 1.5 hours ago
        price: testPrice + 150,
        quantity: 0.5,
        status: 'closed',
        exit_timestamp: currentTime - 1800, // 30 minutes ago
        exit_price: testPrice + 300
    };

    // Test Entry 3: Another open entry (should appear in dropdown)
    const openEntry2 = {
        id: 1003,
        mark_type: 'ENTRY',
        entry_side: 'buy',
        timestamp: currentTime - 3600, // 1 hour ago
        price: testPrice - 100,
        quantity: 2.0,
        status: 'open'
    };

    // Add marks to collection
    window.markingTools.marks.set(1001, openEntry1);
    window.markingTools.marks.set(1002, closedEntry);
    window.markingTools.marks.set(1003, openEntry2);

    console.log('✅ Test marks created:', {
        openEntries: 2,
        closedEntries: 1,
        totalMarks: window.markingTools.marks.size
    });

    // Test dropdown filtering
    console.log('🔍 Testing dropdown filtering...');

    // Create a test select element
    const testSelect = document.createElement('select');
    testSelect.innerHTML = '<option value="">Select an open entry...</option>';

    // Populate with the filtering logic
    window.markingTools.populateOpenEntries(testSelect);

    const options = Array.from(testSelect.options).slice(1); // Skip first option
    const optionTexts = options.map(opt => opt.textContent);

    console.log('📋 Dropdown options found:', options.length);
    console.log('   Options:', optionTexts);

    // Verify filtering
    const hasOpenEntry1 = options.some(opt => opt.value === '1001');
    const hasClosedEntry = options.some(opt => opt.value === '1002');
    const hasOpenEntry2 = options.some(opt => opt.value === '1003');

    console.log('📊 Filtering Results:');
    console.log('   Open Entry 1 (should be present):', hasOpenEntry1 ? '✅' : '❌');
    console.log('   Closed Entry (should be absent):', hasClosedEntry ? '❌' : '✅');
    console.log('   Open Entry 2 (should be present):', hasOpenEntry2 ? '✅' : '❌');

    // Test chart scale configurations
    console.log('🔍 Testing chart scale configurations...');

    let priceScaleConfigured = false;
    let timeScaleConfigured = false;

    // Check if chart exists and has the configurations
    if (window.chartManager && window.chartManager.chart) {
        console.log('📊 Chart manager found, checking configurations...');

        // Check price scale configuration
        try {
            const priceScaleOptions = window.chartManager.chart.priceScale('right').options();
            priceScaleConfigured = !!priceScaleOptions.tickMarkFormatter;
            console.log('   Price scale configured:', priceScaleConfigured ? '✅' : '❌');
        } catch (error) {
            console.log('   Price scale check failed:', error.message);
        }

        // Check time scale configuration
        try {
            const timeScaleOptions = window.chartManager.chart.timeScale().options();
            timeScaleConfigured = !!timeScaleOptions.tickMarkFormatter;
            console.log('   Time scale configured:', timeScaleConfigured ? '✅' : '❌');
        } catch (error) {
            console.log('   Time scale check failed:', error.message);
        }
    } else {
        console.log('⚠️  Chart manager not available for scale testing');
    }

    // Test price scale formatting (250-point intervals)
    console.log('🔍 Testing price scale formatting...');
    const testPrices = [47500, 47750, 48000, 48250, 48500];
    testPrices.forEach(price => {
        const interval = 250;
        const rounded = Math.round(price / interval) * interval;
        const shouldShow = Math.abs(price - rounded) < 125;
        console.log(`   Price ${price} -> Rounded ${rounded}, Show: ${shouldShow ? '✅' : '❌'}`);
    });

    // Test time scale formatting (4-hour intervals)
    console.log('🔍 Testing time scale formatting...');
    const testHours = [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22];
    testHours.forEach(hour => {
        const shouldShow = hour % 4 === 0;
        console.log(`   Hour ${hour}:00 -> Show: ${shouldShow ? '✅' : '❌'}`);
    });

    console.log('✅ Dropdown filtering and chart scale test completed!');

    return {
        dropdownFiltering: {
            totalOptions: options.length,
            expectedOptions: 2, // Only open entries
            hasOpenEntry1: hasOpenEntry1,
            hasClosedEntry: hasClosedEntry,
            hasOpenEntry2: hasOpenEntry2,
            filteringWorksCorrectly: hasOpenEntry1 && !hasClosedEntry && hasOpenEntry2
        },
        chartScales: {
            priceScaleConfigured: priceScaleConfigured,
            timeScaleConfigured: timeScaleConfigured,
            chartManagerAvailable: !!(window.chartManager && window.chartManager.chart)
        }
    };
};

// Debug function to test right-click exit modal functionality
window.debugExitModalTrigger = function() {
    console.log('🔧 Debugging Exit Modal Trigger...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Check if marking mode is enabled
    console.log('📋 Marking mode status:', window.markingTools.isMarkingMode);

    if (!window.markingTools.isMarkingMode) {
        console.log('🔄 Enabling marking mode...');
        window.markingTools.enableMarkingMode();
    }

    // Check chart container and event listeners
    console.log('📊 Chart container:', !!window.markingTools.chart?.container);
    console.log('📊 Chart object:', !!window.markingTools.chart);

    // Simulate right-click by directly calling handleChartClick
    console.log('🖱️  Simulating right-click for exit modal...');

    // Create a mock event
    const mockEvent = {
        clientX: 300,
        clientY: 200,
        preventDefault: () => {}
    };

    // Set up mock chart container bounds
    if (window.markingTools.chart?.container) {
        const container = window.markingTools.chart.container;
        const originalGetBoundingClientRect = container.getBoundingClientRect;
        container.getBoundingClientRect = () => ({
            left: 100,
            top: 100,
            width: 800,
            height: 600
        });

        // Call the handler
        try {
            window.markingTools.handleChartClick(mockEvent, 'exit');
            console.log('✅ handleChartClick called successfully');
        } catch (error) {
            console.error('❌ Error in handleChartClick:', error);
        }

        // Restore original method
        container.getBoundingClientRect = originalGetBoundingClientRect;
    } else {
        console.error('❌ Chart container not available for simulation');
    }
};

// Global debug functions for console access
window.debugMarkingTools = () => {
    if (window.markingTools) {
        console.log('🔧 Marking Tools Debug Info:');
        console.log('Marks:', window.markingTools.marks);
        console.log('Current click data:', window.markingTools.currentClickData);
        console.log('Current trade data:', window.markingTools.currentTradeData);
        console.log('Is marking mode:', window.markingTools.isMarkingMode);

        // Also run timestamp alignment debug
        window.markingTools.debugTimestampAlignment();
    } else {
        console.log('❌ Marking tools not initialized');
    }
};

// Additional debug function specifically for timestamp alignment
window.debugTimestamps = () => {
    if (window.markingTools) {
        window.markingTools.debugTimestampAlignment();
    } else {
        console.log('❌ Marking tools not initialized');
    }
};

// Test function to verify timestamp alignment fix
window.testTimestampAlignment = () => {
    if (!window.markingTools) {
        console.log('❌ Marking tools not initialized');
        return;
    }

    console.log('🧪 Testing timestamp alignment fix...');

    // Get current timeframe
    const timeframe = window.markingTools.getCurrentTimeframe();
    console.log(`Current timeframe: ${timeframe}`);

    // Test with different timestamp formats
    const testTimestamps = [
        Date.now() / 1000,  // Current Unix timestamp
        Date.now(),         // Current millisecond timestamp
        '2024-01-15T10:30:00Z', // ISO string
        1705315800          // Fixed Unix timestamp
    ];

    testTimestamps.forEach((timestamp, index) => {
        console.log(`\n🔍 Test ${index + 1}: ${timestamp} (${typeof timestamp})`);
        const aligned = window.markingTools.alignTimestampWithChart(timestamp);
        console.log(`   Aligned: ${aligned}`);
        console.log(`   Original date: ${new Date((typeof timestamp === 'string' ? new Date(timestamp).getTime() : timestamp * (timestamp > 1000000000000 ? 1 : 1000))).toISOString()}`);
        console.log(`   Aligned date:  ${new Date(aligned * 1000).toISOString()}`);
    });

    console.log('\n✅ Timestamp alignment test completed');
};

// Debug function specifically for 2024 marking issue
window.debug2024Markings = () => {
    if (!window.markingTools) {
        console.log('❌ Marking tools not initialized');
        return;
    }

    console.log('🔍 Debugging 2024 marking visibility issue...');

    const chartData = window.markingTools.chart?.candlestickSeries?.data() || [];
    const markers = window.markingTools.chart?.candlestickSeries?.markers() || [];

    console.log(`Chart data points: ${chartData.length}`);
    console.log(`Chart markers: ${markers.length}`);
    console.log(`Stored marks: ${window.markingTools.marks.size}`);

    if (chartData.length > 0) {
        const firstDate = new Date(chartData[0].time * 1000);
        const lastDate = new Date(chartData[chartData.length - 1].time * 1000);
        console.log(`Chart data range: ${firstDate.toISOString()} to ${lastDate.toISOString()}`);
    }

    // Check marks by year
    const marksByYear = {};
    window.markingTools.marks.forEach((mark, id) => {
        const timestamp = mark.entry_timestamp || mark.timestamp;
        const year = new Date(timestamp * 1000).getFullYear();

        if (!marksByYear[year]) marksByYear[year] = [];
        marksByYear[year].push({ id, mark, timestamp });
    });

    console.log('\n📅 Marks by year:');
    Object.keys(marksByYear).sort().forEach(year => {
        console.log(`${year}: ${marksByYear[year].length} marks`);
        marksByYear[year].forEach(({ id, mark, timestamp }) => {
            const date = new Date(timestamp * 1000);
            const hasMarker = markers.some(m => Math.abs(m.time - timestamp) < 60);
            console.log(`   Mark ${id}: ${date.toISOString()} - Visible: ${hasMarker ? '✅' : '❌'}`);
        });
    });

    // Check if 2024 timestamps are being aligned properly
    console.log('\n🎯 Testing 2024 timestamp alignment:');
    const test2024Timestamp = new Date('2024-01-15T10:30:00Z').getTime() / 1000;
    const aligned = window.markingTools.alignTimestampWithChart(test2024Timestamp);
    console.log(`Test 2024 timestamp: ${test2024Timestamp} -> ${aligned}`);
    console.log(`Original: ${new Date(test2024Timestamp * 1000).toISOString()}`);
    console.log(`Aligned:  ${new Date(aligned * 1000).toISOString()}`);
};

// Force refresh all markers with enhanced debugging
window.forceRefreshMarkers = () => {
    if (!window.markingTools) {
        console.log('❌ Marking tools not initialized');
        return;
    }

    console.log('🔄 Force refreshing all markers with enhanced debugging...');

    // Clear existing markers first
    if (window.markingTools.chart?.candlestickSeries) {
        window.markingTools.chart.candlestickSeries.setMarkers([]);
        console.log('🧹 Cleared existing markers');
    }

    // Wait a moment then refresh
    setTimeout(() => {
        window.markingTools.refreshChartMarkers();
        console.log('✅ Markers refreshed');

        // Run debug after refresh
        setTimeout(() => {
            window.debug2024Markings();
        }, 500);
    }, 100);
};

// Test timeframe rounding functionality
window.testTimeframeRounding = () => {
    if (!window.markingTools) {
        console.log('❌ Marking tools not initialized');
        return;
    }

    console.log('🧪 Testing timeframe rounding functionality...');

    const testCases = [
        { timeframe: '15m', timestamp: 1705315823 }, // Random timestamp
        { timeframe: '1h', timestamp: 1705315823 },
        { timeframe: '4h', timestamp: 1705315823 },
        { timeframe: '1d', timestamp: 1705315823 },
        { timeframe: '15m', timestamp: Date.now() / 1000 }, // Current time
    ];

    testCases.forEach(({ timeframe, timestamp }) => {
        // Temporarily set timeframe
        const originalTimeframe = window.currentTimeframe;
        window.currentTimeframe = timeframe;

        const rounded = window.markingTools.roundTimestampToTimeframe(timestamp);
        const aligned = window.markingTools.alignTimestampWithChart(timestamp);

        console.log(`\n📊 Timeframe: ${timeframe}`);
        console.log(`   Original:  ${timestamp} (${new Date(timestamp * 1000).toISOString()})`);
        console.log(`   Rounded:   ${rounded} (${new Date(rounded * 1000).toISOString()})`);
        console.log(`   Aligned:   ${aligned} (${new Date(aligned * 1000).toISOString()})`);

        // Restore original timeframe
        window.currentTimeframe = originalTimeframe;
    });

    console.log('\n✅ Timeframe rounding test completed');
};

// Comprehensive test for marking visibility across timeframes
window.testMarkingVisibility = () => {
    if (!window.markingTools) {
        console.log('❌ Marking tools not initialized');
        return;
    }

    console.log('🔍 Testing marking visibility across timeframes...');

    const timeframes = ['15m', '1h', '4h', '1d'];
    const currentTimeframe = window.markingTools.getCurrentTimeframe();

    console.log(`Current timeframe: ${currentTimeframe}`);
    console.log(`Chart data points: ${window.markingTools.chart?.candlestickSeries?.data()?.length || 0}`);
    console.log(`Stored marks: ${window.markingTools.marks.size}`);
    console.log(`Chart markers: ${window.markingTools.chart?.candlestickSeries?.markers()?.length || 0}`);

    // Test timestamp rounding for each timeframe
    const testTimestamp = Date.now() / 1000;
    timeframes.forEach(tf => {
        const originalTf = window.currentTimeframe;
        window.currentTimeframe = tf;

        const rounded = window.markingTools.roundTimestampToTimeframe(testTimestamp);
        const intervalSeconds = window.markingTools.getTimeframeSeconds(tf);

        console.log(`${tf}: ${intervalSeconds}s intervals, rounded: ${new Date(rounded * 1000).toISOString()}`);

        window.currentTimeframe = originalTf;
    });

    // Check marker sizes
    const barSpacing = window.markingTools.chart?.chart?.timeScale()?.options()?.barSpacing || 6;
    console.log(`Current bar spacing: ${barSpacing}`);

    // Force refresh markers to test visibility
    console.log('\n🔄 Force refreshing markers...');
    window.markingTools.refreshChartMarkers();

    setTimeout(() => {
        const finalMarkerCount = window.markingTools.chart?.candlestickSeries?.markers()?.length || 0;
        console.log(`✅ Final marker count: ${finalMarkerCount}`);
        console.log('🎯 Marking visibility test completed');
    }, 500);
};

// Test zoom-level text visibility
window.testZoomTextVisibility = () => {
    if (!window.markingTools) {
        console.log('❌ Marking tools not initialized');
        return;
    }

    console.log('🔍 Testing zoom-level text visibility...');

    const chart = window.markingTools.chart;
    if (!chart?.chart?.timeScale) {
        console.log('❌ Chart not available');
        return;
    }

    const timeScale = chart.chart.timeScale();
    const currentBarSpacing = timeScale.options().barSpacing || 6;

    console.log(`Current bar spacing: ${currentBarSpacing}`);

    // Test different zoom levels
    const testZoomLevels = [0.5, 1, 2, 4, 6, 8, 12, 16];

    testZoomLevels.forEach(barSpacing => {
        console.log(`\n📊 Testing bar spacing: ${barSpacing}`);

        // Simulate marker properties at this zoom level
        let size, priceText;
        const testPrice = 45000;

        if (barSpacing < 1) {
            size = 6;
            priceText = testPrice >= 1000 ? `${(testPrice / 1000).toFixed(0)}K` : `${testPrice.toFixed(0)}`;
        } else if (barSpacing < 2) {
            size = 5;
            priceText = testPrice >= 1000 ? `$${(testPrice / 1000).toFixed(1)}K` : `$${testPrice.toFixed(0)}`;
        } else if (barSpacing < 4) {
            size = 4;
            priceText = testPrice >= 1000 ? `$${(testPrice / 1000).toFixed(1)}K` : `$${testPrice.toFixed(0)}`;
        } else if (barSpacing < 8) {
            size = 4;
            priceText = `$${testPrice.toFixed(2)}`;
        } else {
            size = 5;
            priceText = `$${testPrice.toFixed(2)}`;
        }

        console.log(`   Size: ${size}, Text: "${priceText}"`);

        // Test text strategies
        let buyText = `BUY @ ${priceText}`;
        let sellText = `SELL @ ${priceText}`;
        let exitText = `EXIT @ ${priceText}`;

        if (barSpacing < 1) {
            buyText = 'B';
            sellText = 'S';
            exitText = 'X';
        } else if (barSpacing < 2) {
            buyText = 'BUY';
            sellText = 'SELL';
            exitText = 'EXIT';
        }

        console.log(`   BUY text: "${buyText}"`);
        console.log(`   SELL text: "${sellText}"`);
        console.log(`   EXIT text: "${exitText}"`);
    });

    // Force refresh with current zoom level
    console.log('\n🔄 Refreshing markers with current zoom level...');
    window.markingTools.refreshChartMarkers();

    console.log('✅ Zoom text visibility test completed');
};
